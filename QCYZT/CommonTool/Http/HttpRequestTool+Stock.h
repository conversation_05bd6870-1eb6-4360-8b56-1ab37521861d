//
//  HttpRequestTool+Stock.h
//  QCYZT
//
//  Created by macPro on 2019/2/19.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "HttpRequestTool.h"


@interface HttpRequestTool (Stock)

#pragma mark 券商列表
+(void)requestBrokerListStart:(void (^)())startBlock
                      failure:(void (^)())failBlock
                      success:(requestSuccessBlock)success;

#pragma mark - 自选股接口 2.0版
/**
 *  添加自选
 *
 *  @param code       股票代码
 *  @param name       股票名称
 */
+ (void)marketAddSelfStockWithCode:(NSString*)code
                              name:(NSString*)name
                             index:(NSString*)index
                           groupId:(NSString *)groupId
                             start:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success;

/**
 *  删除自选股
 *
 *  @param code       股票代码
 */
+ (void)marketDeleteSelfStockWithCode:(NSString*)code
                              groupId:(NSString *)groupId
                                start:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success;

// 同步多支股票到服务器
+ (void)syncMultipleSelfStocksToServerWithStocks:(NSArray *)stocks
                                      groupId:(NSString *)groupId
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 检查是否有指标权限
+ (void)checkIndexAuthorityWithStart:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;

#pragma mark - 自选股分组
// 添加分组
+ (void)addSelfStockGroupWithGroupName:(NSString*)groupName groupSort:(NSString*)groupSort start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 修改分组名
+ (void)editSelfStockGroupWithGroupId:(NSString *)groupId groupName:(NSString*)groupName start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 修改分组排序
+ (void)sortSelfStockGroupWithData:(NSArray *)data start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 我的自选分组列表
+ (void)getSelfStockGroupsWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 删除分组
+ (void)deleteSelfStockGroupWithGroupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 分组下自选股
+ (void)stockListInSelfStockGroupWithGroupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 自选股所在分组
+ (void)querySelfStockGroupContainStock:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 修改分组内股票排序
+ (void)sortStockInSelfStockGroupWithData:(NSArray *)data start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;


#pragma mark  - 龙虎榜
// 股票
+ (void)requestWinnerStockWithType:(NSInteger)type tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 股票游资榜
+ (void)requestWinnerYouZiStockWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 股票详情
+ (void)requestWinnerStockDetailWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 股票上榜日期
+ (void)requestWinnerStockDetailRankDayWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 股票详情区间统计
+ (void)requestWinnerStockDetailRangeStatisticWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay filter:(NSInteger )filter start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 股票详情连榜统计
+ (void)requestWinnerStockDetailRankingStatisticWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 营业部详情
+ (void)requestSalesDepartmentDetailWithBoCode:(NSString *)boCode filter:(NSInteger )filter start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

#pragma mark - 实时龙虎榜
/**
 * 实时龙虎榜列表
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 * @param orderType 排序类型 0 正序 1倒叙
 * @param orderParam 排序字段 0 最新价 1 涨幅 2 主力净额 3人气值 4 主力买 5主力卖 6 区间涨幅 7 涨速 8 成交额 9 总市值 10 流通市值 11 实际流通 12 卖流占比 13 净流占比 14 换手率 15 量比 16振幅 17 第一季度机构持仓 18 竞价量比 19 竞价涨幅 20 竞价金额  ， 默认排序为2
 *
 */
+ (void)requestRealTimeWinnerStockListWithStartIndex:(NSInteger)startIndex
                                            endIndex:(NSInteger)endIndex
                                                date:(NSString *)date
                                              showKc:(BOOL)showKc
                                              showSt:(BOOL)showSt
                                         isSelfStock:(BOOL)isSelfStock
                                           orderType:(BOOL)orderType
                                          orderParam:(NSInteger)orderParam
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success;

/**
 * 实时龙虎榜区间统计
 * @param startBlock 请求开始回调
 * @param failBlock 请求失败回调
 * @param success 请求成功回调
 * @param orderType 排序类型 0 正序 1倒叙
 * @param orderParam 排序字段 0 当前涨幅 1区间净额 2 主力买 3主力卖 4 区间成交 5价格 6 总市值 7 流通市值 8 实际流通 9 换手率 10 量比 11 振幅 12 第一季度机构持仓 13 竞价量比 14 竞价涨幅 15 竞价金额  默认排序为1
 *
 */
+ (void)requestRealTimeWinnerStockRangeStatisticWithStartIndex:(NSInteger)startIndex
                                                      endIndex:(NSInteger)endIndex
                                                     startTime:(NSString *)startTime
                                                       endTime:(NSString *)endTime
                                                        showKc:(BOOL)showKc
                                                        showSt:(BOOL)showSt
                                                   isSelfStock:(BOOL)isSelfStock
                                                     orderType:(BOOL)orderType
                                                    orderParam:(NSInteger)orderParam
                                                         start:(void (^)())startBlock
                                                       failure:(void (^)())failBlock
                                                       success:(requestSuccessBlock)success;

#pragma mark - 指标商城
// 指标商城列表
+ (void)requestIndexMallListWithPage:(NSUInteger)page
                            pageSize:(NSUInteger)pageSize
                            category:(NSString *)category
                               query:(NSString *)query
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;

// 根据code查询字典列表
+ (void)requestListByCode:(NSString *)code
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                  success:(requestSuccessBlock)success;

// banner列表
+ (void)requestIndexMallBannerListWithStart:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                    success:(requestSuccessBlock)success;

// 指标详情
+ (void)requestIndexDetailWithFormulaId:(NSInteger)formulaId
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success;

// 指标支付信息
+ (void)requestIndexPayInfoWithFormulaId:(NSInteger)formulaId
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success;

// 已购指标列表
+ (void)requestPurchasedIndexListWithPage:(NSUInteger)page
                            pageSize:(NSUInteger)pageSize
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 获取所有指标
+ (void)requestIndexMallListWithStart:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success;

// 指标模板列表
+ (void)requestIndexMallTemplateListWithStart:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 请求模板指标
+ (void)requestIndexAndTemplateList;
+ (void)requestIndexAndTemplateListWithCompletion:(void (^)(BOOL success))completionBlock;


#pragma mark - 板块
// 热门板块
+ (void)requestBlockHotPlateWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success;


// 资金净流入排名简要
+ (void)requestBlockFundFlowSummaryWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success;

// 板块涨跌幅排名简要
+ (void)requestBlockQuoteSummaryWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 资金流向列表
+ (void)requestBlockFundFlowListWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 板块列表
+ (void)requestBlockPlateListWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                               success:(requestSuccessBlock)success;

// 板块详情成分股
+ (void)requestBlockDetailConstituentWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 板块详情行情
+ (void)requestBlockDetailHqWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 板块 K线
/// type:k线类型  1：1分钟K线；2：5分钟K线；3：15分钟K线；4：30分钟K线；5：60分钟K线；6：日K线；7：周K线；8：月K线；9：年K线；13：120分钟K线；15：季K线
+ (void)requestBlockDetailKLineWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                success:(requestSuccessBlock)success;

// 分时
+ (void)requestBlockDetailMinuteWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success;

// 股票所属概念
+ (void)requestBlockStockConceptWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 股票详情所属板块
+ (void)requestStockDetailBelongBlockWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                           success:(requestSuccessBlock)success;

// 股票所属板块
+ (void)requestStockBelongBlockWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success;

// 基金详情持仓股
+ (void)requestFundDetailHoldingsWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                       success:(requestSuccessBlock)success;

#pragma mark - 涨停聚焦
// 涨停强度统计
+ (void)requestLimitUpStatisticWithTradingDay:(NSString *)tradingDay
                                       status:(NSInteger)status
                                         isHS:(BOOL)isHS
                                         isKC:(BOOL)isKC
                                         isST:(NSString *)isST
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success;

// 涨停强度股池, status当前状态(1涨停池2炸板池3跌停池5涨停冲刺9连扳池)
+ (void)requestLimitUpPoolsWithTradingDay:(NSString *)tradingDay
                                   pageNo:(NSInteger)pageNo
                                 pageSize:(NSInteger)pageSize
                                   status:(NSInteger)status
                                     isHS:(BOOL)isHS
                                     isKC:(BOOL)isKC
                                     isST:(NSString *)isST
                                    start:(void (^)())startBlock
                                  failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 最强风口
+ (void)requestStrongestTrendWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 连板天梯统计
+ (void)requestContinuousUpStatisticWithTradingDay:(NSString *)tradingDay filterST:(BOOL)filterST start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 连板天梯
+ (void)requestContinuousLimitLaddarWithTradingDay:(NSString *)tradingDay filterST:(BOOL)filterST start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

#pragma mark - 解套宝
// 解套宝详情
+ (void)requestUnlockPositionDetailWithStockCode:(NSString *)stockCode price:(CGFloat)price nowPrice:(CGFloat)nowPrice start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 获取商品信息，以及是否购买权限
+ (void)requestUnlockPositionGetGoodsInfoWithStart:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success;

#pragma mark - 大宗交易
// 市场情况
+ (void)requestBigDealMarketSituationWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 折线图
+ (void)requestBigDealLineWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 交易记录
+ (void)requestBigDealRecordWithSearchKey:(NSString *)searchKey
                               tradingDay:(NSString *)tradingDay
                                      pageNo:(NSInteger)pageNo
                                    pageSize:(NSInteger)pageSize
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

// 个股详情大宗交易整体情况
+ (void)requestStockDetailBigDealStockStatWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 个股详情大宗交易整体情况统计列表
+ (void)requestStockDetailBigDealStockListWithStockCode:(NSString *)stockCode  start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

#pragma mark - 涨停
// 历史涨跌表现
+ (void)requestStockDetailLimitUpStatWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 收盘价折线图
+ (void)requestStockDetailLimitUpLinesWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 历史涨跌停明细
+ (void)requestStockDetailLimitUpRecordsWithStockCode:(NSString *)stockCode
                                               pageNo:(NSInteger)pageNo
                                             pageSize:(NSInteger)pageSize
                                                start:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success;

#pragma mark - 融资融券
// 个股整体统计
+ (void)requestStockDetailRZRQSynonymsStatWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 个股折线图
+ (void)requestStockDetailRZRQGraphListWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 个股交易明细
+ (void)requestStockDetailRZRQDealDetailsWithStockCode:(NSString *)stockCode
                                      pageNo:(NSInteger)pageNo
                                    pageSize:(NSInteger)pageSize
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success;

// 融资融券情况
+ (void)requestRZRQConditionStatWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 融资融券市场详细数据
+ (void)requestRZRQMarketDetailDataWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 融资融券走势图
+ (void)requestRZRQTrendGraphWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 融资净买入列表
+ (void)requestRZRQRzjmrListWithTradingDay:(NSString *)tradingDay
                                    pageNo:(NSInteger)pageNo
                                  pageSize:(NSInteger)pageSize
                                isOptional:(BOOL)isOptional
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success;

// 两融余额列表
+ (void)requestRZRQLryeListWithTradingDay:(NSString *)tradingDay
                                    pageNo:(NSInteger)pageNo
                                  pageSize:(NSInteger)pageSize
                                isOptional:(BOOL)isOptional
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success;

#pragma mark - 行情列表
// 推荐位
+ (void)requestStockListRecommendedWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 市场概况
+ (void)requestStockListMarketOverviewWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;
+ (void)requestStockListMarketOverview2WithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 龙虎榜简要
+ (void)requestStockListMarketWinnerRankWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 涨停聚焦图
+ (void)requestStockListZTJJGraphWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

// 涨停聚焦统计
+ (void)requestStockListZTJJStatisticsWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success;

@end

