//
//  HttpRequestTool+Stock.m
//  QCYZT
//
//  Created by macPro on 2019/2/19.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "HttpRequestTool+Stock.h"
#import "FMUPDataTool.h"
#import "FMIndicatorImportUtil.h"
 

//--------------------  自选股  --------------------
#define kAPI_SelfStocks_Add     KDakaBaseUrl(@"/api/v2/selfStock/add.do")              // 添加自选股
#define kAPI_SelfStocks_Delete  KDakaBaseUrl(@"/api/v2/selfStock/delete.do")           // 删除自选股
#define kAPI_SelfStocks_Sync    KDakaBaseUrl(@"/api/v2/selfStock/sync.do")             // 自选股同步（添加多支）

//--------------------  自选股分组相关  --------------------
#define kAPI_SelfStockGroup_Add     KDakaBaseUrl(@"/api/v2/selfGroup/add.do")        // 添加自选股分组
#define kAPI_SelfStockGroup_Edit     KDakaBaseUrl(@"/api/v2/selfGroup/edit.do")        // 编辑自选股分组
#define kAPI_SelfStockGroup_ChangeIndex     KDakaBaseUrl(@"/api/v2/selfGroup/changeIndexs")        // 修改自选股分组排序
#define kAPI_SelfStockGroup_List     KDakaBaseUrl(@"/api/v2/selfGroup/list.do")        // 获取我的自选股列表
#define kAPI_SelfStockGroup_Delete     KDakaBaseUrl(@"/api/v2/selfGroup/delete.do")        // 删除自选股分组
#define kAPI_SelfStockGroup_Stock     KDakaBaseUrl(@"/api/v2/selfStock/groupSelfStocks")        // 分组下的自选股
#define kAPI_SelfStockGroup_QueryGroup     KDakaBaseUrl(@"/api/v2/selfGroup/queryGroup")        // 自选股所在分组
#define kAPI_SelfStockGroup_ChangeStockIndex  KDakaBaseUrl(@"/api/v2/selfStock/changeIndexs")     // 修改分组内自选股顺序


//--------------------  券商管理  --------------------
#define kAPI_Broker_List KDakaBaseUrl(@"/api/broker/list.do")     // 券商列表

//--------------------  指标权限  --------------------
#define kAPI_Index_Authority KDakaBaseUrl(@"/api/v2/umr")     // 指标权限

//--------------------  龙虎榜  --------------------
#define kAPI_Winner_stock KDakaBaseUrl(@"/api/v2/sd/stock")     // 股票
#define kAPI_Winner_stockYouzi KDakaBaseUrl(@"/api/v2/sd/hotMoney")     // 游资榜
#define kAPI_Winner_stockDetail KDakaBaseUrl(@"/api/v2/sd/stock/dayDetail")     // 个股详情
#define kAPI_Winner_stockDetailRankDay KDakaBaseUrl(@"/api/v2/sd/stock/day")     // 上榜日期
#define kAPI_Winner_stockDetailRange KDakaBaseUrl(@"/api/v2/sd/stock/rangeDetail")     // 个股详情区间统计
#define kAPI_Winner_stockDetailRanking KDakaBaseUrl(@"/api/v2/sd/stock/continue")     // 个股详情连榜统计
#define kAPI_Winner_departmentDetail KDakaBaseUrl(@"/api/v2/sd/department/detail")     // 营业部详情

static NSString *const kAPI_Winner_RealTimeList = @"/api/hq/app/lhb/orderList"; // 实时龙虎榜列表
static NSString *const kAPI_Winner_RealTimeRangeStatistic = @"/api/hq/app/lhb/historyOrder"; // 实时龙虎榜区间统计


//--------------------  指标、模板策略  --------------------
#define kAPI_IndexMall_QueryListByCode      KDakaBaseUrl(@"/api/v2/dic/queryListByCode")     // 根据code查询字典列表
#define kAPI_IndexMall_Banner               KDakaBaseUrl(@"/api/v2/indexPermission/bannerList.do")     // 指标商城banner
#define kAPI_IndexMall_List                 KDakaBaseUrl(@"/api/v2/indexPermission/list.do")     // 指标商城list
#define kAPI_IndexMall_IndexDetail          KDakaBaseUrl(@"/api/v2/indexPermission/getFormula.do")     // 指标详情
#define kAPI_IndexMall_IndexPayInfo          KDakaBaseUrl(@"/api/v2/indexPermission/getFormulaPay.do")     // 指标支付信息
#define kAPI_IndexMall_IndexPurchased          KDakaBaseUrl(@"/api/v2/indexPermission/haspayList")     // 已购指标列表


#define kAPI_IndexMall_QueryAll             KDakaBaseUrl(@"/api/v2/indexPermission/queryAll")     // 获取云指标列表
#define kAPI_IndexMall_TemplateList         KDakaBaseUrl(@"/api/v2/indexPermission/templateList2")     // 指标模板列表



//--------------------  板块  --------------------
#define kAPI_Block_HotPlate   KDakaBaseUrl(@"/api/hq/app/plate/hotPlate")     // 热门板块
#define kAPI_Block_FundFlowSummary   KDakaBaseUrl(@"/api/hq/app/plate/fundflowSummary")     // 资金净流入排名简要
#define kAPI_Block_QuoteSummary   KDakaBaseUrl(@"/api/hq/app/plate/quoteSummary")     // 板块涨跌幅排名简要

#define kAPI_Block_FundFlowList   KDakaBaseUrl(@"/api/hq/app/plate/fundflow")     // 资金流向列表
#define kAPI_Block_PlateList   KDakaBaseUrl(@"/api/hq/app/plate/quote")     // 板块列表

#define kAPI_Block_DetailConstituent    KDakaBaseUrl(@"/api/hq/app/plate/constituent")     // 成分股
#define kAPI_Block_DetailHq             KDakaBaseUrl(@"/api/hq/app/plate/real")     // 板块行情
#define kAPI_Block_KLine             KDakaBaseUrl(@"/api/hq/app/plate/kline")     // K 线
#define kAPI_Block_Minute             KDakaBaseUrl(@"/api/hq/app/plate/trend")     // 分时
#define kAPI_Block_Minute5             KDakaBaseUrl(@"/api/hq/app/plate/trend5")     // 5日分时

#define kAPI_Block_StockConcept        KDakaBaseUrl(@"/api/hq/app/stock/concept")     // 股票所属概念
#define kAPI_Stock_StockBelongBlock        KDakaBaseUrl(@"/api/hq/app/plate/belongConcept")     // 股票详情所属板块
#define kAPI_Stock_StockBelongBlock2        KDakaBaseUrl(@"/api/hq/app/lhb/belongConcept")     // 股票所属板块

#define kAPI_Fund_DetailHoldings    KDakaBaseUrl(@"/api/hq/app/fundHold")     // 基金成分股


//--------------------  涨停聚焦  --------------------
#define kAPI_ZTJJ_LimitUpStatistic   KDakaBaseUrl(@"/api/hq/app/ztjjPool/upIntensityStatistic")     // 涨停强度统计
#define kAPI_ZTJJ_LimitUpPools   KDakaBaseUrl(@"/api/hq/app/ztjjPool/queryPools")     // 涨停聚焦股池
#define kAPI_ZTJJ_StrongestTrend   KDakaBaseUrl(@"/api/hq/app/ztjjPool/topWind")     // 最强风口
#define kAPI_ZTJJ_ContinuousUpStatistic   KDakaBaseUrl(@"/api/hq/app/ztjjPool/continuousUpStatistic")     // 连板天梯整体
#define kAPI_ZTJJ_ContinuousLimitLaddar  KDakaBaseUrl(@"/api/hq/app/ztjjPool/continuousUp")     // 连板天梯

//--------------------  解套宝  --------------------
#define kAPI_UnlockPosition_Detail   KDakaBaseUrl(@"/api/v2/jtb/detail")     // 解套宝详情
#define kAPI_UnlockPosition_GoodsInfo   KDakaBaseUrl(@"/api/v2/jtb/getGoodsInfo")     // 解套宝商品信息

//--------------------  大宗交易  --------------------
#define kAPI_BigDeal_MarketSituation   KDakaBaseUrl(@"/api/hq/app/blocktrading/stat")     // 市场情况
#define kAPI_BigDeal_StatLine          KDakaBaseUrl(@"/api/hq/app/blocktrading/line")     // 折线图
#define kAPI_BigDeal_DealRecord        KDakaBaseUrl(@"/api/hq/app/blocktrading/page")     // 交易记录
#define kAPI_StockDetail_BigDeal_StockStat   KDakaBaseUrl(@"/api/hq/app/blocktrading/stockStat")     // 个股大宗交易整体情况统计
#define kAPI_StockDetail_BigDeal_StockList  KDakaBaseUrl(@"/api/hq/app/blocktrading/stockList")     // 个股大宗交易整体情况统计列表


//--------------------  融资融券  --------------------
#define kAPI_StockDetail_RZRQ_SynonymsStat   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailStat")     // 个股整日情况统计
#define kAPI_StockDetail_RZRQ_GraphList   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailGraphList")     // 个股折线图
#define kAPI_StockDetail_RZRQ_TradingDetail  KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailPage")     // 个股交易明细

#define kAPI_RZRQ_ConditionStat   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailStat2")     // 情况统计
#define kAPI_RZRQ_MarketDetailData   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailMarketStat")     // 各市场详细数据
#define kAPI_RZRQ_Trend   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingdetailGraph")     // 融资融券走势
#define kAPI_RZRQ_RZJMRList   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingStatPage")     // 融资净买入 分页查询
#define kAPI_RZRQ_LRYEList   KDakaBaseUrl(@"/api/hq/app/blocktrading/mtTradingPage")     // 两融余额 分页查询



//--------------------  涨停  --------------------
#define kAPI_StockDetail_LimitUp_historyStat   KDakaBaseUrl(@"/api/hq/app/zt/ztHistoryStat")     // 历史涨停表现
#define kAPI_StockDetail_LimitUp_Lines   KDakaBaseUrl(@"/api/hq/app/zt/ztLine")     // 折线图
#define kAPI_StockDetail_LimitUp_Record   KDakaBaseUrl(@"/api/hq/app/zt/ztHistoryDetail")     // 历史涨跌停明细


//-------------------- 行情列表  --------------------
#define kAPI_StockList_Recommended  KDakaBaseUrl(@"/api/v2/recommonContent/markert")     // 行情页面推荐
#define kAPI_StockList_MarketOverview  KDakaBaseUrl(@"/api/hq/app/stock/marketOverview")     // 大盘概况
#define kAPI_StockList_MarketOverview2  KDakaBaseUrl(@"/api/hq/app/stock/yzbMarketOverview")  // 大盘概况——资金流入
#define kAPI_StockList_WinnerList  KDakaBaseUrl(@"/api/hq/app/stock/dragonBrief")     // 龙虎榜简要
#define kAPI_StockList_ZTJJGraph    KDakaBaseUrl(@"/api/hq/app/ztjjPool/getGraph")     // 涨停聚焦图
#define kAPI_StockList_ZTJJStatistics    KDakaBaseUrl(@"/api/hq/app/ztjjPool/statHqZtDto")     // 涨停聚焦统计



@implementation HttpRequestTool (Stock)

#pragma mark 请求股票接口
// 券商列表 3.0
+(void)requestBrokerListStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Broker_List
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark -- 自选股   2.0版本
// 添加自选
+ (void)marketAddSelfStockWithCode:(NSString*)code
                              name:(NSString*)name
                             index:(NSString*)index
                           groupId:(NSString *)groupId
                             start:(void (^)())startBlock
                           failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (code) {
        [params setObject:code forKey:@"stockCode"];
    }
    if (index) {
        [params setObject:index forKey:@"index"];
    }
    if (name) {
        [params setObject:name forKey:@"stockName"];
    }
    if (groupId.length) {
        [params setObject:groupId forKey:@"groupId"];
    }
    [self postDataJsonInfoWithUrl:kAPI_SelfStocks_Add params:params start:startBlock failure:failBlock success:success];
}

// 删除自选
+ (void)marketDeleteSelfStockWithCode:(NSString*)code groupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (code) {
        [params setObject:code forKey:@"stockCode"];
    }
    if (groupId.length) {
        [params setObject:groupId forKey:@"groupId"];
    }
    [self getDataInfoWithUrl:kAPI_SelfStocks_Delete
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


// 同步多支股票到服务器
+ (void)syncMultipleSelfStocksToServerWithStocks:(NSArray *)stocks groupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableArray *arr = [NSMutableArray arrayWithCapacity:stocks.count];
    for (FMSelfOptionStockModel *model in stocks) {
        NSString *code = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:[model.setCode integerValue] code:model.code]];
        [arr addObject:@{@"stockCode":code, @"stockName":model.name}];
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[JsonTool jsonStringFromDicOrArr:arr] forKey:@"stocks"];
    if (groupId.length) {
        [params setObject:groupId forKey:@"groupIds"];
    }
    [self postDataInfoWithUrl:kAPI_SelfStocks_Sync params:params start:startBlock failure:failBlock success:success];
}


// 检查是否有指标权限
+ (void)checkIndexAuthorityWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Index_Authority
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark - 自选股分组
// 添加分组
+ (void)addSelfStockGroupWithGroupName:(NSString*)groupName groupSort:(NSString*)groupSort start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[FMUserDefault getUserId] forKey:@"userId"];
    [params setObject:groupName forKey:@"groupName"];
    [params setObject:groupSort forKey:@"groupSort"];
    [self postDataJsonInfoWithUrl:kAPI_SelfStockGroup_Add params:params start:startBlock failure:failBlock success:success];
}

// 修改分组名
+ (void)editSelfStockGroupWithGroupId:(NSString *)groupId groupName:(NSString*)groupName start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[FMUserDefault getUserId] forKey:@"userId"];
    [params setObject:groupName forKey:@"groupName"];
    [params setObject:groupId forKey:@"id"];
    [self postDataJsonInfoWithUrl:kAPI_SelfStockGroup_Edit params:params start:startBlock failure:failBlock success:success];
}

// 修改分组排序
+ (void)sortSelfStockGroupWithData:(NSArray *)data start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:data forKey:@"selfGroups"];
    [self postDataJsonInfoWithUrl:kAPI_SelfStockGroup_ChangeIndex params:params start:startBlock failure:failBlock success:success];
}

// 我的自选分组列表
+ (void)getSelfStockGroupsWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_SelfStockGroup_List params:nil start:startBlock failure:failBlock success:success];
}

// 删除分组
+ (void)deleteSelfStockGroupWithGroupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (groupId) {
        [params setObject:groupId forKey:@"id"];
    }
    [self getDataInfoWithUrl:kAPI_SelfStockGroup_Delete
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 分组下自选股
+ (void)stockListInSelfStockGroupWithGroupId:(NSString *)groupId start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (groupId.length) {
        [params setObject:groupId forKey:@"groupId"];
    } else {
        [params setObject:@"0" forKey:@"groupId"];
    }
    [self getDataInfoWithUrl:kAPI_SelfStockGroup_Stock
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 自选股所在分组
+ (void)querySelfStockGroupContainStock:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (stockCode) {
        [params setObject:stockCode forKey:@"stockCode"];
    }
    [self getDataInfoWithUrl:kAPI_SelfStockGroup_QueryGroup
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


// 修改分组内股票排序
+ (void)sortStockInSelfStockGroupWithData:(NSArray *)data start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:data forKey:@"selfStocks"];
    [self postDataJsonInfoWithUrl:kAPI_SelfStockGroup_ChangeStockIndex params:params start:startBlock failure:failBlock success:success];
}

#pragma mark  - 龙虎榜
// 股票
+ (void)requestWinnerStockWithType:(NSInteger)type tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (!tradingDay.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(type) forKey:@"type"];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_Winner_stock
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票游资榜
+ (void)requestWinnerYouZiStockWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_Winner_stockYouzi
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票详情
+ (void)requestWinnerStockDetailWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (!tradingDay.length || !stockCode.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_Winner_stockDetail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票上榜日期
+ (void)requestWinnerStockDetailRankDayWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (!stockCode.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Winner_stockDetailRankDay
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票详情区间统计
+ (void)requestWinnerStockDetailRangeStatisticWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay filter:(NSInteger )filter start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (filter < 1 || !stockCode.length || !tradingDay.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(filter) forKey:@"filter"];
    [params setObject:stockCode forKey:@"stockCode"];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_Winner_stockDetailRange
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票详情连榜统计
+ (void)requestWinnerStockDetailRankingStatisticWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (!tradingDay.length || !stockCode.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Winner_stockDetailRanking
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 营业部详情
+ (void)requestSalesDepartmentDetailWithBoCode:(NSString *)boCode filter:(NSInteger )filter start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    if (filter < 1 || !boCode.length) {
        return;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(filter) forKey:@"filter"];
    [params setObject:boCode forKey:@"boCode"];
    [self getDataInfoWithUrl:kAPI_Winner_departmentDetail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark - 龙虎榜实时
// 实时龙虎榜列表
+ (void)requestRealTimeWinnerStockListWithStartIndex:(NSInteger)startIndex
                                            endIndex:(NSInteger)endIndex
                                                date:(NSString *)date
                                              showKc:(BOOL)showKc
                                              showSt:(BOOL)showSt
                                         isSelfStock:(BOOL)isSelfStock
                                           orderType:(BOOL)orderType
                                          orderParam:(NSInteger)orderParam
                                               start:(void (^)())startBlock
                                             failure:(void (^)())failBlock
                                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(startIndex) forKey:@"startIndex"];
    [params setObject:@(endIndex) forKey:@"endIndex"];
    [params setObject:@(showKc) forKey:@"showKc"];
    [params setObject:@(showSt) forKey:@"showSt"];
    [params setObject:@(orderType) forKey:@"orderType"];
    [params setObject:@(orderParam) forKey:@"orderParam"];
    if (date.length) {
        [params setObject:date forKey:@"date"];
    }
    if (isSelfStock) {
        NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
        NSMutableArray *tmpArr = [NSMutableArray array];
        for (NSInteger i = 0; i < selfStocks.count; i++) {
            FMSelfOptionStockModel *model = selfStocks[i];
            NSString *code = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:[model.setCode integerValue] code:model.code]] ;
            [tmpArr addObject:code];
        }
        [params setObject:tmpArr forKey:@"stockCodes"];
    }
    [self cancelHttpRequestWithUrlString:KDakaBaseUrl(kAPI_Winner_RealTimeList)];
    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_Winner_RealTimeList)
                       params:params
                        start:startBlock
                      failure:failBlock
                      success:success];
}

// 实时龙虎榜区间统计
+ (void)requestRealTimeWinnerStockRangeStatisticWithStartIndex:(NSInteger)startIndex
                                                      endIndex:(NSInteger)endIndex
                                                     startTime:(NSString *)startTime
                                                       endTime:(NSString *)endTime
                                                        showKc:(BOOL)showKc
                                                        showSt:(BOOL)showSt
                                                   isSelfStock:(BOOL)isSelfStock
                                                     orderType:(BOOL)orderType
                                                    orderParam:(NSInteger)orderParam
                                                         start:(void (^)())startBlock
                                                       failure:(void (^)())failBlock
                                                       success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(startIndex) forKey:@"startIndex"];
    [params setObject:@(endIndex) forKey:@"endIndex"];
    [params setObject:@(showKc) forKey:@"showKc"];
    [params setObject:@(showSt) forKey:@"showSt"];
    [params setObject:@(orderType) forKey:@"orderType"];
    [params setObject:@(orderParam) forKey:@"orderParam"];
    if (startTime.length) {
        [params setObject:startTime forKey:@"startTime"];
    }
    if (endTime.length) {
        [params setObject:endTime forKey:@"endTime"];
    }
    if (isSelfStock) {
        NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
        NSMutableArray *tmpArr = [NSMutableArray array];
        for (NSInteger i = 0; i < selfStocks.count; i++) {
            FMSelfOptionStockModel *model = selfStocks[i];
            NSString *code = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:[model.setCode integerValue] code:model.code]] ;
            [tmpArr addObject:code];
        }
        [params setObject:tmpArr forKey:@"stockCodes"];
    }
    [self cancelHttpRequestWithUrlString:KDakaBaseUrl(kAPI_Winner_RealTimeRangeStatistic)];
    [self postDataJsonInfoWithUrl:KDakaBaseUrl(kAPI_Winner_RealTimeRangeStatistic)
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

#pragma mark - 指标商城
// banner列表
+ (void)requestIndexMallBannerListWithStart:(void (^)())startBlock
                                    failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_IndexMall_Banner
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 根据code查询字典列表
+ (void)requestListByCode:(NSString *)code
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (code) {
        [params setObject:code forKey:@"code"];
    }

    [self getDataInfoWithUrl:kAPI_IndexMall_QueryListByCode
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 指标列表
+ (void)requestIndexMallListWithPage:(NSUInteger)page
                            pageSize:(NSUInteger)pageSize
                            category:(NSString *)category
                               query:(NSString *)query
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    if (category) {
        [params setObject:category forKey:@"categoryType"];
    }
    if (query.length) {
        [params setObject:query forKey:@"indexName"];
    }
    
    [self postDataJsonInfoWithUrl:kAPI_IndexMall_List
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 指标详情
+ (void)requestIndexDetailWithFormulaId:(NSInteger)formulaId
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(formulaId) forKey:@"formulaId"];
    
    [self getDataInfoWithUrl:kAPI_IndexMall_IndexDetail
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 指标支付信息
+ (void)requestIndexPayInfoWithFormulaId:(NSInteger)formulaId
start:(void (^)())startBlock
failure:(void (^)())failBlock
success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:@(formulaId) forKey:@"formulaId"];
    
    [self getDataInfoWithUrl:kAPI_IndexMall_IndexPayInfo
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 已购指标列表
+ (void)requestPurchasedIndexListWithPage:(NSUInteger)page
                            pageSize:(NSUInteger)pageSize
                               start:(void (^)())startBlock
                             failure:(void (^)())failBlock
                             success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    
    [self postDataJsonInfoWithUrl:kAPI_IndexMall_IndexPurchased
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 获取所有指标
+ (void)requestIndexMallListWithStart:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_IndexMall_QueryAll
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 指标模板列表
+ (void)requestIndexMallTemplateListWithStart:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_IndexMall_TemplateList
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

+ (void)requestIndexAndTemplateList {
    [HttpRequestTool requestIndexMallListWithStart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<FMIndexMallListModel *> *arr = [NSArray modelArrayWithClass:[FMIndexMallListModel class] json:dic[@"data"]];
            [FMIndicatorImportUtil deleteAllNetIndicators];
            [FMIndicatorImportUtil importNetIndicatorsWithArray:arr];
        }
    }];

    [HttpRequestTool requestIndexMallTemplateListWithStart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<FMIndexMallTemplateGroupModel *> *arr = [NSArray modelArrayWithClass:[FMIndexMallTemplateGroupModel class] json:dic[@"data"]];
            [FMIndicatorImportUtil importTemplateIndicatorsWithArray:arr];
        }
    }];
}

+ (void)requestIndexAndTemplateListWithCompletion:(void (^)(BOOL success))completionBlock {
    // 创建 dispatch group
    dispatch_group_t group = dispatch_group_create();
    
    // 标志位，确保 block 只执行一次
    __block BOOL hasCompleted = NO;
    
    // 创建一个 0.25秒的超时计时器
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 如果还没完成两个请求，超时后调用失败回调
        if (!hasCompleted) {
            hasCompleted = YES; // 标记为已完成
            if (completionBlock) {
                completionBlock(NO);
            }
        }
    });
    
    // 请求1：requestIndexMallList
    dispatch_group_enter(group);
    [HttpRequestTool requestIndexMallListWithStart:^{
    } failure:^{
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<FMIndexMallListModel *> *arr = [NSArray modelArrayWithClass:[FMIndexMallListModel class] json:dic[@"data"]];
            [FMIndicatorImportUtil deleteAllNetIndicators];
            [FMIndicatorImportUtil importNetIndicatorsWithArray:arr];
        }
        dispatch_group_leave(group);
    }];
    
    // 请求2：requestIndexMallTemplateList
    dispatch_group_enter(group);
    [HttpRequestTool requestIndexMallTemplateListWithStart:^{
    } failure:^{
        dispatch_group_leave(group);
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray<FMIndexMallTemplateGroupModel *> *arr = [NSArray modelArrayWithClass:[FMIndexMallTemplateGroupModel class] json:dic[@"data"]];
            [FMIndicatorImportUtil importTemplateIndicatorsWithArray:arr];
        }
        dispatch_group_leave(group);
    }];
    
    // 监听 group 完成
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        // 如果超时还没发生，返回成功
        if (!hasCompleted) {
            hasCompleted = YES; // 标记为已完成
            if (completionBlock) {
                completionBlock(YES);
            }
        } else {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"indexAndTemplateListUpdate" object:nil];
        }
    });
}




#pragma mark - 板块
// 热门板块
+ (void)requestBlockHotPlateWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Block_HotPlate
                params:nil
                 start:startBlock
               failure:failBlock
               success:success];
}

// 资金净流入排名简要
+ (void)requestBlockFundFlowSummaryWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Block_FundFlowSummary
                params:nil
                 start:startBlock
               failure:failBlock
               success:success];
}

// 板块涨跌幅排名简要
+ (void)requestBlockQuoteSummaryWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Block_QuoteSummary
                params:nil
                 start:startBlock
               failure:failBlock
               success:success];
}

// 板块的资金流向列表
+ (void)requestBlockFundFlowListWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Block_FundFlowList
                params:nil
                 start:startBlock
               failure:failBlock
               success:success];
}

// 板块列表
+ (void)requestBlockPlateListWithStart:(void (^)())startBlock
                              failure:(void (^)())failBlock
                           success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Block_PlateList
                params:nil
                 start:startBlock
               failure:failBlock
               success:success];
}

// 板块详情成分股，1 涨幅榜 2 跌幅榜 3 换手率榜(Integer)
+ (void)requestBlockDetailConstituentWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (type > 0) {
        [params setObject:[NSString stringWithFormat:@"%zd", type] forKey:@"type"];
    }
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Block_DetailConstituent
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 板块详情行情
+ (void)requestBlockDetailHqWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Block_DetailHq
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 板块 K线
/// type:k线类型  1：1分钟K线；2：5分钟K线；3：15分钟K线；4：30分钟K线；5：60分钟K线；6：日K线；7：周K线；8：月K线；9：年K线；13：120分钟K线；15：季K线
+ (void)requestBlockDetailKLineWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (type > 0) {
        [params setObject:[NSString stringWithFormat:@"%zd", type] forKey:@"type"];
    }
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Block_KLine
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 分时
+ (void)requestBlockDetailMinuteWithType:(NSInteger)type
                                    stockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    NSString *url;
    if (type == 1) {
        url = kAPI_Block_Minute;
    } else {
        url = kAPI_Block_Minute5;
    }
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:url
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票所属概念
+ (void)requestBlockStockConceptWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Block_StockConcept
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票详情所属板块
+ (void)requestStockDetailBelongBlockWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Stock_StockBelongBlock
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 股票所属板块
+ (void)requestStockBelongBlockWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Stock_StockBelongBlock2
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 基金详情持仓股
+ (void)requestFundDetailHoldingsWithStockCode:(NSString *)stockCode
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_Fund_DetailHoldings
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}


#pragma mark - 涨停聚焦
// 涨停强度统计
+ (void)requestLimitUpStatisticWithTradingDay:(NSString *)tradingDay 
                                       status:(NSInteger)status
                                         isHS:(BOOL)isHS
                                         isKC:(BOOL)isKC
                                         isST:(NSString *)isST
                                        start:(void (^)())startBlock
                                      failure:(void (^)())failBlock
                                      success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
//    [params setObject:@(status) forKey:@"status"];
    [params setObject:[NSString stringWithFormat:@"%d", isHS] forKey:@"isHs"];
    [params setObject:[NSString stringWithFormat:@"%d", isKC] forKey:@"isKc"];
    [params setObject:isST forKey:@"isSt"];
    [self postDataJsonInfoWithUrl:kAPI_ZTJJ_LimitUpStatistic
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 涨停强度股池, status当前状态(1涨停池2炸板池3跌停池5涨停冲刺9连扳池)
+ (void)requestLimitUpPoolsWithTradingDay:(NSString *)tradingDay 
                                   pageNo:(NSInteger)pageNo
                                 pageSize:(NSInteger)pageSize
                                   status:(NSInteger)status
                                     isHS:(BOOL)isHS
                                     isKC:(BOOL)isKC
                                     isST:(NSString *)isST
                                    start:(void (^)())startBlock
                                  failure:(void (^)())failBlock
                                  success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [params setObject:@(status) forKey:@"status"];
    [params setObject:[NSString stringWithFormat:@"%d", isHS] forKey:@"isHs"];
    [params setObject:[NSString stringWithFormat:@"%d", isKC] forKey:@"isKc"];
    [params setObject:isST forKey:@"isSt"];
    [self postDataJsonInfoWithUrl:kAPI_ZTJJ_LimitUpPools
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}  

// 最强风口
+ (void)requestStrongestTrendWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_ZTJJ_StrongestTrend
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 连板天梯统计
+ (void)requestContinuousUpStatisticWithTradingDay:(NSString *)tradingDay filterST:(BOOL)filterST start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [params setObject:@(filterST) forKey:@"stFlag"];
    [self getDataInfoWithUrl:kAPI_ZTJJ_ContinuousUpStatistic
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 连板天梯
+ (void)requestContinuousLimitLaddarWithTradingDay:(NSString *)tradingDay filterST:(BOOL)filterST start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [params setObject:@(filterST) forKey:@"stFlag"];
    [self getDataInfoWithUrl:kAPI_ZTJJ_ContinuousLimitLaddar
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

#pragma mark - 解套宝
// 解套宝详情
+ (void)requestUnlockPositionDetailWithStockCode:(NSString *)stockCode price:(CGFloat)price nowPrice:(CGFloat)nowPrice start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [params setObject:@(price) forKey:@"price"];
    [params setObject:[NSString stringWithFormat:@"%.2f", nowPrice] forKey:@"nowPrice"];

    [self getDataInfoWithUrl:kAPI_UnlockPosition_Detail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 获取商品信息，以及是否购买权限
+ (void)requestUnlockPositionGetGoodsInfoWithStart:(void (^)())startBlock
                                         failure:(void (^)())failBlock
                                         success:(requestSuccessBlock)success {
    
    [self getDataInfoWithUrl:kAPI_UnlockPosition_GoodsInfo
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

#pragma mark - 大宗交易
// 市场情况
+ (void)requestBigDealMarketSituationWithTradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [self getDataInfoWithUrl:kAPI_BigDeal_MarketSituation
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 折线图
+ (void)requestBigDealLineWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_BigDeal_StatLine
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 交易记录
+ (void)requestBigDealRecordWithSearchKey:(NSString *)searchKey
                               tradingDay:(NSString *)tradingDay
                                      pageNo:(NSInteger)pageNo
                                    pageSize:(NSInteger)pageSize
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (searchKey.length) {
        [params setObject:searchKey forKey:@"searchKey"];
    }
    if (tradingDay.length) {
        [params setObject:tradingDay forKey:@"tradingDay"];
    }
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];

    [self getDataInfoWithUrl:kAPI_BigDeal_DealRecord
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 个股详情大宗交易整体情况
+ (void)requestStockDetailBigDealStockStatWithStockCode:(NSString *)stockCode tradingDay:(NSString *)tradingDay start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:tradingDay forKey:@"tradingDay"];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_BigDeal_StockStat
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 个股详情大宗交易整体情况统计列表
+ (void)requestStockDetailBigDealStockListWithStockCode:(NSString *)stockCode  start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_BigDeal_StockList
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

#pragma mark - 融资融券
// 个股整体统计
+ (void)requestStockDetailRZRQSynonymsStatWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_RZRQ_SynonymsStat
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 个股折线图
+ (void)requestStockDetailRZRQGraphListWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_RZRQ_GraphList
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 个股交易明细
+ (void)requestStockDetailRZRQDealDetailsWithStockCode:(NSString *)stockCode
                                      pageNo:(NSInteger)pageNo
                                    pageSize:(NSInteger)pageSize
                                       start:(void (^)())startBlock
                                     failure:(void (^)())failBlock
                                     success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [self getDataInfoWithUrl:kAPI_StockDetail_RZRQ_TradingDetail
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 融资融券情况
+ (void)requestRZRQConditionStatWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_RZRQ_ConditionStat
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 融资融券市场详细数据
+ (void)requestRZRQMarketDetailDataWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [self getDataInfoWithUrl:kAPI_RZRQ_MarketDetailData
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 融资融券走势图
+ (void)requestRZRQTrendGraphWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_RZRQ_Trend
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 融资净买入列表
+ (void)requestRZRQRzjmrListWithTradingDay:(NSString *)tradingDay
                                    pageNo:(NSInteger)pageNo
                                  pageSize:(NSInteger)pageSize
                                isOptional:(BOOL)isOptional
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (tradingDay.length) {
        [params setObject:tradingDay forKey:@"tradingDay"];
    }
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [params setObject:@(isOptional) forKey:@"isOptional"];
    if (isOptional) {
        NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
        NSMutableArray *tmpArr = [NSMutableArray array];
        for (NSInteger i = 0; i < selfStocks.count; i++) {
            FMSelfOptionStockModel *model = selfStocks[i];
            NSString *code = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:[model.setCode integerValue] code:model.code]] ;
            [tmpArr addObject:code];
        }
        [params setObject:tmpArr forKey:@"stockCodeList"];
    }
    [self postDataJsonInfoWithUrl:kAPI_RZRQ_RZJMRList
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}

// 两融余额列表
+ (void)requestRZRQLryeListWithTradingDay:(NSString *)tradingDay
                                    pageNo:(NSInteger)pageNo
                                  pageSize:(NSInteger)pageSize
                                isOptional:(BOOL)isOptional
                                     start:(void (^)())startBlock
                                   failure:(void (^)())failBlock
                                   success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (tradingDay.length) {
        [params setObject:tradingDay forKey:@"tradingDay"];
    }
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [params setObject:@(isOptional) forKey:@"isOptional"];
    if (isOptional) {
        NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
        NSMutableArray *tmpArr = [NSMutableArray array];
        for (NSInteger i = 0; i < selfStocks.count; i++) {
            FMSelfOptionStockModel *model = selfStocks[i];
            NSString *code = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:[model.setCode integerValue] code:model.code]] ;
            [tmpArr addObject:code];
        }
        [params setObject:tmpArr forKey:@"stockCodeList"];
    }
    
    [self postDataJsonInfoWithUrl:kAPI_RZRQ_LRYEList
                           params:params
                            start:startBlock
                          failure:failBlock
                          success:success];
}


#pragma mark - 涨停
// 历史涨跌表现
+ (void)requestStockDetailLimitUpStatWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_LimitUp_historyStat
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 收盘价折线图
+ (void)requestStockDetailLimitUpLinesWithStockCode:(NSString *)stockCode start:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [self getDataInfoWithUrl:kAPI_StockDetail_LimitUp_Lines
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 历史涨跌停明细
+ (void)requestStockDetailLimitUpRecordsWithStockCode:(NSString *)stockCode
                                               pageNo:(NSInteger)pageNo
                                             pageSize:(NSInteger)pageSize
                                                start:(void (^)())startBlock
                                              failure:(void (^)())failBlock
                                              success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:stockCode forKey:@"stockCode"];
    [params setObject:@(pageNo) forKey:@"pageNo"];
    [params setObject:@(pageSize) forKey:@"pageSize"];
    [self getDataInfoWithUrl:kAPI_StockDetail_LimitUp_Record
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

#pragma mark - 行情列表
// 推荐位
+ (void)requestStockListRecommendedWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_Recommended
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 市场概况
+ (void)requestStockListMarketOverviewWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_MarketOverview
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

+ (void)requestStockListMarketOverview2WithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_MarketOverview2
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 龙虎榜简要
+ (void)requestStockListMarketWinnerRankWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_WinnerList
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 涨停聚焦图
+ (void)requestStockListZTJJGraphWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_ZTJJGraph
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 涨停聚焦统计
+ (void)requestStockListZTJJStatisticsWithStart:(void (^)())startBlock failure:(void (^)())failBlock success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_StockList_ZTJJStatistics
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}


@end
