//
//  PayPointsCell.h
//  QCYZT
//
//  Created by Augment on 2025-07-30.
//  Copyright © 2025年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^PointsSelectionChangedBlock)(NSInteger usePoints);

@interface PayPointsCell : UITableViewCell

/// 配置积分cell
/// @param freeDesc 免费说明文本
/// @param userPoints 用户积分数
/// @param orderAmount 订单金额
/// @param couponDiscount 优惠券抵扣金额
/// @param pointsRatio 积分与金币的兑换比例（多少积分=1金币）
/// @param selectionChangedBlock 选择状态改变回调
- (void)configureWithFreeDesc:(NSString *)freeDesc
                   userPoints:(NSInteger)userPoints
                  orderAmount:(NSInteger)orderAmount
               couponDiscount:(NSInteger)couponDiscount
                  pointsRatio:(NSInteger)pointsRatio
        selectionChangedBlock:(PointsSelectionChangedBlock)selectionChangedBlock;

/// 更新订单信息（当优惠券或价格变化时调用）
- (void)updateOrderAmount:(NSInteger)orderAmount couponDiscount:(NSInteger)couponDiscount;

@end

NS_ASSUME_NONNULL_END
