//
//  UPMarket2SDIStockFundMinuteView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIStockFundMinuteView.h"
#define UPFundDashLineTop 25*UPHeightScale
#define UPFundDashLineHeight 150*UPHeightScale

#define kNolValue   -300000   // 无值特殊标志

// MARK: - UPMarket2SDIStockFundInfo

@interface UPMarket2SDIStockFundInfo : NSObject

@property(nonatomic, assign) int time;                                      // 分时 - 零点以来的分钟数；K线 - 日期：YYYYMMDD
@property(nonatomic, assign) double mainMoneyInlow;                         // 累计主力资金净流入
@property(nonatomic, assign) double singleMainMoneyInlow;                         // 单日主力资金净流入

@end


// MARK: - UPMarket2SDIStockFundInfo

@implementation UPMarket2SDIStockFundInfo

@end

// MARK: - UPMarket2SDIStockFundInfoPosition

@interface UPMarket2SDIStockFundInfoPosition : NSObject

@property(nonatomic, assign) BOOL isIn;                                      // 是否主力流入
@property(nonatomic, assign) CGPoint startPoint;                            // 起始点
@property(nonatomic, assign) CGPoint endPoint;                              // 结束点

@end


// MARK: - UPMarket2SDIStockFundInfoPosition

@implementation UPMarket2SDIStockFundInfoPosition

@end

@interface UPMarket2SDIStockFundMinuteView ()

@property (nonatomic, assign) CGRect drawViewRect;  // 绘图的范围

@property (nonatomic, assign) NSInteger level;  // 横向分几格
/**
 交易时间
 */
@property (nonatomic, strong) NSArray<NSArray<NSNumber *>*> *tradeTimeArray;
//计算后数据源
@property (nonatomic, strong) NSArray <UPMarket2SDIStockFundInfo *> *fundInfoList;
//计算后坐标
@property (nonatomic, strong) NSArray<NSArray *> *drawPositionModels;
@property (nonatomic, strong) NSArray<NSArray<UPMarket2SDIStockFundInfoPosition *>*> *singlePositionModels;

//最大值和最小值
@property(nonatomic, assign) long long maxValue;
@property(nonatomic, assign) long long minValue;

@property (nonatomic, assign) CGFloat lineGap;

//当前长按十字线对应位置
@property (nonatomic, assign) CGPoint selectedPoint;
@property (nonatomic, assign) NSInteger selectedIndex;
//当前长按选中的资金信息
@property (nonatomic, strong) UPMarket2SDIStockFundInfo * selectedFundInfo;

//长按计时timer
@property(nonatomic, strong) NSTimer *longPressTimer;
@property (nonatomic, assign) BOOL longPress;

@end

@implementation UPMarket2SDIStockFundMinuteView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.level = 4;
        
        //长按手势（需求没有，暂时注释）
        UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(event_longPressAction:)];
        [self addGestureRecognizer:longPress];
        
        self.alpha = 0;
        [UIView animateWithDuration:0.5 animations:^{
            self.alpha = 1;
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.drawViewRect =  CGRectMake(UPWidth(15), UPWidth(33), self.up_width - UPWidth(30), self.up_height-UPWidth(33)-UPWidth(30));
}

- (void)dealloc {
    [self.longPressTimer invalidate];
    self.longPressTimer = nil;
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
    
    //绘制背景线
    [self drawDashLine];
    //绘制底部时间
    [self drawBottomTradeTime];
    //绘制图形
    [self drawLine];
    
    if (_longPress) {
        [self drawLongPressWithPoint:self.selectedPoint];
    }
    //绘制顶部时间和主力净流入
    [self drawTopFundInfo];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self stopTimer];
}

- (void)drawLine {
    // 1. 左边值
    if (self.maxValue != kNolValue) {
        UIColor *textColor = UIColor.up_textSecondary1Color;
        double value = MAX(ABS(self.maxValue), ABS(self.minValue));
        NSString *yText = [NSString stringWithFormat:@"%@",[UPMarketUICalculateUtil transStringWithUnitOfValue:@(value) afterPoint:0]];
        [UPMarketUIDrawTool drawTextWithTextString:yText fontSize:UPWidth(10) color:textColor x:CGRectGetMinX(self.drawViewRect) + UPWidth(5) y:CGRectGetMinY(self.drawViewRect) + UPWidth(5)];
        
        yText = [NSString stringWithFormat:@"-%@",[UPMarketUICalculateUtil transStringWithUnitOfValue:@(value) afterPoint:0]];
        [UPMarketUIDrawTool drawTextWithTextString:yText fontSize:UPWidth(10) color:textColor x:CGRectGetMinX(self.drawViewRect) + UPWidth(5) y:CGRectGetMaxY(self.drawViewRect) - UPWidth(15)];
    }

    // 2. 画图
    for (UPMarket2SDIStockFundInfoPosition *pModel in self.singlePositionModels.lastObject) {
        UIColor *lineColor = pModel.isIn ? UIColor.upmarketui_riseColor : UIColor.upmarketui_fallColor;
        CGPoint endPoint = pModel.endPoint;
        if (pModel.startPoint.y == pModel.endPoint.y) {
            lineColor = UIColor.upmarketui_equalColor;
            endPoint = CGPointMake(pModel.endPoint.x, pModel.endPoint.y-1);
        } else if (ABS(pModel.startPoint.y - pModel.endPoint.y < 1)){
            endPoint = CGPointMake(pModel.endPoint.x, pModel.endPoint.y-1);
        }
        [UPMarketUIDrawTool drawLineWithArray:@[[NSValue valueWithCGPoint:pModel.startPoint],[NSValue valueWithCGPoint:endPoint]] lineWidth:self.lineGap lineColor:lineColor];
    }
    
    // 3. 画折线（需求没有，先注释）
//    [UPMarketUIDrawTool drawLineWithArray:self.drawPositionModels.lastObject lineWidth:1.5 lineColor:UIColor.upmarketui_averagePriceLineColor];
}

/**
 绘制背景线
 */
- (void)drawDashLine {
    UIColor *lineColor = UIColor.upmarketui_borderColor;
    // 1. 绘制外框
    [UPMarketUIDrawTool drawRect:self.drawViewRect strokeColor:lineColor fillColor:UIColor.up_contentBgColor lineWidth:0.5];
    // 2. 绘制横线
    CGPoint startPoint = CGPointMake(CGRectGetMinX(self.drawViewRect), CGRectGetMinY(self.drawViewRect) + CGRectGetHeight(self.drawViewRect)/2);
    CGPoint EndPoint = CGPointMake(CGRectGetMaxX(self.drawViewRect), CGRectGetMinY(self.drawViewRect) + CGRectGetHeight(self.drawViewRect)/2);
    
    [UPMarketUIDrawTool drawDashLineWithStartPoint:startPoint endPoint:EndPoint lineWidth:0.5 unitLength:UPWidth(10) margin:UPWidth(5) lineColor:lineColor];
    
    // 3. 绘制竖线
    CGFloat padding = CGRectGetWidth(self.drawViewRect)/self.level-1;
    for (int i = 0; i < self.level-1; i++) {
        CGFloat x = CGRectGetMinX(self.drawViewRect)+(padding*(i+1));
        CGPoint startPoint = CGPointMake(x, CGRectGetMinY(self.drawViewRect));
        CGPoint EndPoint = CGPointMake(x, CGRectGetMaxY(self.drawViewRect));
        
        [UPMarketUIDrawTool drawDashLineWithStartPoint:startPoint endPoint:EndPoint lineWidth:0.5 unitLength:UPWidth(10) margin:UPWidth(5) lineColor:lineColor];
    }
    
}

/**
 绘制顶部时间和主力净流入
 */
- (void)drawTopFundInfo {
    UIColor *textColor = UIColor.up_textSecondaryColor;
    [UPMarketUIDrawTool drawTextWithTextString:@"主力净流入" fontSize:UPWidth(13) color:textColor x:CGRectGetMinX(self.drawViewRect) y:CGRectGetMinY(self.drawViewRect) - UPWidth(18)];
    
//    [UPMarketUIDrawTool drawTextWithTextString:@"单位：万元" fontSize:UPWidth(13) color:textColor x:CGRectGetMaxX(self.drawViewRect) - UPWidth(65) y:CGRectGetMinY(self.drawViewRect) - UPWidth(18)];
}


- (void)drawLongPressWithPoint:(CGPoint)point {
    if (self.drawViewRect.size.height == 0 || self.drawViewRect.size.width == 0) {
        return;
    }
    
    if (self.selectedIndex >= 0 && self.selectedIndex < self.singlePositionModels.lastObject.count) {
        UPMarket2SDIStockFundInfoPosition *position = self.singlePositionModels.lastObject[self.selectedIndex];
        point.y = position.endPoint.y;
        if (position.startPoint.y == position.endPoint.y) {
            point.y = position.endPoint.y - 1;
        } else if (ABS(position.startPoint.y - position.endPoint.y < 1)){
            point.y = position.endPoint.y - 1;
        }
    }
    
    if (point.y > CGRectGetMaxY(self.drawViewRect)) {
        point.y = CGRectGetMaxY(self.drawViewRect);
    } else if (point.y < CGRectGetMinY(self.drawViewRect)){
        point.y = CGRectGetMinY(self.drawViewRect);
    }
    
    [UPMarketUIDrawTool drawLineWithStartPoint:CGPointMake(CGRectGetMinX(self.drawViewRect), point.y) endPoint:CGPointMake(CGRectGetMaxX(self.drawViewRect), point.y) lineWidth:0.5 lineColor:UIColor.upmarketui_axisLineColor];
    
    [UPMarketUIDrawTool drawLineWithStartPoint:CGPointMake(point.x, CGRectGetMinY(self.drawViewRect)) endPoint:CGPointMake(point.x, CGRectGetMaxY(self.drawViewRect)) lineWidth:0.5 lineColor:UIColor.upmarketui_axisLineColor];
    
    NSInteger startIndex = self.selectedIndex;
    
    UPMarket2SDIStockFundInfo *obj = nil;
    if (startIndex >= 0 && startIndex < self.fundInfoList.count) {
        obj = self.fundInfoList[startIndex];
    }
    self.selectedFundInfo = obj;
    if (obj) {
        {
            NSDate *date = [NSDate up_dateFromSecond:obj.time];
            BOOL isThisYear1 = [self thisYear:date];
            NSString *text = isThisYear1 ? [date up_formatDate:kUPDateFormat_MMdd_WithMinus] : [date up_formatDate:kUPDateFormat_yyyyMMdd_WithMinus];
            CGFloat bgWidth = [UPMarketUIDrawTool rectOfNSString:text fontSize:10].size.width + 5;
            CGFloat bgX =  point.x - bgWidth/2;
            if (point.x + bgWidth / 2 > CGRectGetMaxX(self.drawViewRect)) {
                bgX = CGRectGetMaxX(self.drawViewRect) - bgWidth;
            } else if (point.x - bgWidth / 2 < CGRectGetMinX(self.drawViewRect)) {
                bgX = CGRectGetMinX(self.drawViewRect);
            }
            CGRect bgRect = CGRectMake(bgX, CGRectGetMaxY(self.drawViewRect), bgWidth, UPWidth(15));
            [UPMarketUIDrawTool drawRect:bgRect strokeColor:UIColor.upmarketui_crossColor fillColor:UIColor.upmarketui_crossRectColor lineWidth:0.5];
            [UPMarketUIDrawTool drawTextWithTextString:text fontSize:UPWidth(10) color:UIColor.upmarketui_crossTextColor centerPoint:CGPointMake(bgX + bgWidth/2,CGRectGetMaxY(self.drawViewRect)+UPWidth(7))];
        }
        
        {
            BOOL isRight = point.x < CGRectGetMidX(self.drawViewRect);
            
            double value = 0;
            if (self.selectedIndex >= 0 && self.selectedIndex < self.fundInfoList.count) {
                value = self.fundInfoList[self.selectedIndex].singleMainMoneyInlow;
            }
                
            NSString *text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@(value) afterPoint:0];
            CGFloat bgHeight = [UPMarketUIDrawTool rectOfNSString:text fontSize:10].size.height;
            CGFloat bgWidth = [UPMarketUIDrawTool rectOfNSString:text fontSize:10].size.width;
            
            CGFloat bgY =  point.y - bgHeight/2;
            if (bgY < CGRectGetMinY(self.drawViewRect)) {
                bgY = CGRectGetMinY(self.drawViewRect);
            } else if (bgY + bgHeight > CGRectGetMaxY(self.drawViewRect)) {
                bgY = CGRectGetMaxY(self.drawViewRect) - bgHeight;
            }
            
            CGRect bgRect = CGRectMake(isRight ? CGRectGetMaxX(self.drawViewRect) - bgWidth : CGRectGetMinX(self.drawViewRect), bgY, bgWidth, bgHeight);
            [UPMarketUIDrawTool drawRect:bgRect strokeColor:UIColor.upmarketui_crossColor fillColor:UIColor.upmarketui_crossRectColor lineWidth:0.5];
            [UPMarketUIDrawTool drawTextWithTextString:text fontSize:UPWidth(10) color:UIColor.upmarketui_crossTextColor centerPoint:CGPointMake(CGRectGetMidX(bgRect),CGRectGetMidY(bgRect))];
        }
    }
}

/**
 绘制底部时间
 */
- (void)drawBottomTradeTime {
    if (!IsValidateArray(self.fundInfoList)) return;
    
    NSDate *date1 = [NSDate up_dateFromSecond:self.fundInfoList.firstObject.time];
    NSDate *date2 = [NSDate up_dateFromSecond:self.fundInfoList.lastObject.time];
    
    BOOL isThisYear1 = [self thisYear:date1];
    BOOL isThisYear2 = [self thisYear:date2];
    
    NSString *time1 = isThisYear1 ? [date1 up_formatDate:kUPDateFormat_MMdd_WithMinus] : [date1 up_formatDate:kUPDateFormat_yyyyMMdd_WithMinus];
    NSString *time2 = isThisYear2 ? [date2 up_formatDate:kUPDateFormat_MMdd_WithMinus] : [date2 up_formatDate:kUPDateFormat_yyyyMMdd_WithMinus];
    
    UIColor *textColor = UIColor.up_textSecondary1Color;
    {
        [UPMarketUIDrawTool drawTextWithTextString:time1 fontSize:UPWidth(10) color:textColor x:CGRectGetMinX(self.drawViewRect) y:CGRectGetMaxY(self.drawViewRect) + UPWidth(5)];
    }
    {
        CGRect rect = [UPMarketUIDrawTool rectOfNSString:time2 fontSize:UPWidth(10)];
        [UPMarketUIDrawTool drawTextWithTextString:time2 fontSize:UPWidth(10) color:textColor x:CGRectGetMaxX(self.drawViewRect) - rect.size.width y:CGRectGetMaxY(self.drawViewRect) + UPWidth(5)];
    }
}

-(BOOL)thisYear:(NSDate *)date {
    NSCalendar *cale = [NSCalendar currentCalendar];
    
    NSCalendarUnit unit = NSCalendarUnitYear;
    
    NSDateComponents *nowCmps = [cale components:unit fromDate:[NSDate date]];
    NSDateComponents *selfCmps = [cale components:unit fromDate:date];
    
    return nowCmps.year == selfCmps.year;
}

/**
 请求数据
 */
- (void)requestData:(UPHqStockHq *)stockHq
{
    [self requestForTimeWithStockHq:stockHq];
}

/**
 请求交易时间
 */
- (void)requestForTimeWithStockHq:(UPHqStockHq *)stockHq {
    self.totolTradeMinutes = 20;
}


- (void)setDdeDataList:(NSArray<UPMarketDDEInfo *> *)ddeDataList {
    if (_ddeDataList && !ddeDataList) {
        return;
    }

    if (!self.totolTradeMinutes) {
        [self requestData:self.stockHq];
    }
    
    _ddeDataList = ddeDataList;

    double zjlx = 0;
    double max = 0;
    double min = 0;
    double singleMax = -DBL_MAX;
    double singleMin = DBL_MAX;
    NSMutableArray *fundDatas;
    
    if (ddeDataList.count) {
        fundDatas = [NSMutableArray array];
        for (int i = 0; i < ddeDataList.count; i++) {
            UPMarketDDEInfo *ddeInfo = ddeDataList[i];
            UPMarket2SDIStockFundInfo *fundInfo = [UPMarket2SDIStockFundInfo new];
            fundInfo.time = ddeInfo.time;
            if (ddeInfo.amountItem){
                CGFloat singleMainMoneyInlow = ddeInfo.amountItem.superIn + ddeInfo.amountItem.bigIn - ddeInfo.amountItem.superOut - ddeInfo.amountItem.bigOut;
                fundInfo.singleMainMoneyInlow = singleMainMoneyInlow;
                zjlx += singleMainMoneyInlow;
                fundInfo.mainMoneyInlow = zjlx;
            }
            
            if (i == 0) {
                max = min = fundInfo.mainMoneyInlow;
            } else {
                max =  MAX(max, fundInfo.singleMainMoneyInlow);
                min =  MIN(min, fundInfo.singleMainMoneyInlow);
            }
            
            singleMax =  MAX(singleMax, fundInfo.singleMainMoneyInlow);
            singleMin =  MIN(singleMin, fundInfo.singleMainMoneyInlow);
            
            [fundDatas addObject:fundInfo];
        }
    }
    
    self.fundInfoList = fundDatas;
    if (max == min) {
        if (min < 0) {
            max = 0;
        } else if (min > 0) {
            min = 0;
        } else {
            max = kNolValue;
        }
    }
    
    if (singleMax == -DBL_MAX || singleMin == DBL_MAX) {
        singleMax = singleMin = 0;
    }
    
    self.maxValue = max;
    self.minValue = min;

    CGFloat startX = CGRectGetMinX(self.drawViewRect);  // 起始X
    
    double value = MAX(ABS(self.maxValue), ABS(self.minValue));
    NSString *yText = [NSString stringWithFormat:@"%@",[UPMarketUICalculateUtil transStringWithUnitOfValue:@(value) afterPoint:0]];
    CGSize textSize = [UPMarketUIDrawTool rectOfNSString:yText fontSize:UPWidth(10)].size;
    
    // 预留高度，避免贴顶
    CGFloat minY = CGRectGetMinY(self.drawViewRect) + textSize.height + UPWidth(5);   // 最小Y
    CGFloat maxY = CGRectGetMaxY(self.drawViewRect) - UPWidth(15);   // 最大Y
    CGFloat h_half = (maxY - minY)/2; // 高一半
    CGFloat zeroY = minY + h_half - 0.5;  // 中间Y值
    CGFloat sunitValue = [UPMarketUICalculateUtil transWithoutINForNAN:MAX(ABS(singleMax), ABS(singleMin))/h_half];  // Y上的单位
    
    CGFloat gap = self.lineGap;
    if (self.totolTradeMinutes > 0) {
        gap = (self.drawViewRect.size.width / (self.totolTradeMinutes*2-1));
        self.lineGap = gap;
    }

    if (self.fundInfoList.count) {
        NSMutableArray *tempDrawPositionArray = [NSMutableArray array];
        NSMutableArray *tempSingleDrawPositionArray = [NSMutableArray array];
        NSMutableArray *temp = [NSMutableArray array];
        NSMutableArray *tempSingle = [NSMutableArray array];
        for (int i = 0; i < self.fundInfoList.count; i++) {
            UPMarket2SDIStockFundInfo *fundInfo = self.fundInfoList[i];
            CGFloat xPosition = startX + i * (gap*2) + gap/2;
            CGFloat syPosition;
            if (sunitValue == 0) {
                syPosition = zeroY;
            } else {
                if (fundInfo.singleMainMoneyInlow > 0) {
                    syPosition = zeroY - ABS(fundInfo.singleMainMoneyInlow/sunitValue);
                } else {
                    syPosition = zeroY + ABS(fundInfo.singleMainMoneyInlow/sunitValue);
                }
            }
            CGPoint spoint = CGPointMake(xPosition, syPosition);
            [temp addObject:[NSValue valueWithCGPoint:spoint]];
            
            CGPoint zeroPoint = CGPointMake(xPosition, zeroY);
            UPMarket2SDIStockFundInfoPosition *position = [UPMarket2SDIStockFundInfoPosition new];
            position.isIn = fundInfo.singleMainMoneyInlow > 0;
            position.startPoint = zeroPoint;
            position.endPoint = spoint;
            [tempSingle addObject:position];
        }
        
        [tempDrawPositionArray addObject:temp];
        [tempSingleDrawPositionArray addObject:tempSingle];
        
        self.drawPositionModels = tempDrawPositionArray;
        self.singlePositionModels = tempSingleDrawPositionArray;
    }
    
    self.selectedFundInfo = self.fundInfoList.lastObject;

    [self setNeedsDisplay];
}


- (void)event_longPressAction:(UILongPressGestureRecognizer *)longPress {
    
    CGPoint location = [longPress locationInView:self];
    
    //3.拿到中心点数据源的index
    __block int centerIndex = 0;
    __block double minWidth = ABS(self.singlePositionModels.firstObject.firstObject.startPoint.x - location.x);
    [self.singlePositionModels.firstObject enumerateObjectsUsingBlock:^(UPMarket2SDIStockFundInfoPosition * _Nonnull positionModel, NSUInteger idx, BOOL * _Nonnull stop) {
        if (ABS(positionModel.startPoint.x - location.x) < minWidth) {
            centerIndex = idx;
            minWidth = ABS(positionModel.startPoint.x - location.x);
        }
    }];
    
    if (location.x > CGRectGetMaxX(self.drawViewRect)) {
        centerIndex = self.singlePositionModels.firstObject.count-1;
    }
    
    self.selectedPoint = CGPointMake(self.singlePositionModels.firstObject[centerIndex].startPoint.x, location.y);
    self.selectedIndex = centerIndex;
    
    if (longPress.state == UIGestureRecognizerStateBegan) {
        self.longPress = YES;
        
        [self.longPressTimer invalidate];
        self.longPressTimer = nil;
    }
    
    
    if(longPress.state == UIGestureRecognizerStateEnded || longPress.state == UIGestureRecognizerStateCancelled || longPress.state == UIGestureRecognizerStateFailed) {
        [self.longPressTimer invalidate];
        self.longPressTimer = [NSTimer scheduledTimerWithTimeInterval:5 target:self selector:@selector(stopTimer) userInfo:nil repeats:NO];
    }
    
    [self setNeedsDisplay];
}

- (void)setStockHq:(UPHqStockHq *)stockHq {
    [super setStockHq:stockHq];
    
    [self requestData:stockHq];
}

- (void)setTotolTradeMinutes:(NSInteger)totolTradeMinutes {
    _totolTradeMinutes = totolTradeMinutes;
    
    CGFloat gap = (self.drawViewRect.size.width / (totolTradeMinutes*2-1));
    self.lineGap = gap;
}


- (void)stopTimer {
    if (self.longPress) {
        self.longPress = NO;
        //恢复scrollView的滑动
        [self setDdeDataList:_ddeDataList];
    }
    
    [self.longPressTimer invalidate];
    self.longPressTimer = nil;
}

@end
