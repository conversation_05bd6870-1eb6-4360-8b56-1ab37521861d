//
//  FMGlobalRequestInterface.h
//  QCYZT
//
//  Created by macPro on 2018/4/27.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#ifndef FMGlobalRequestInterface_h
#define FMGlobalRequestInterface_h


// --------------------------  接口地址  ---------------------
#if DEBUG
// 测试环境
#define kDakaInit    @"https://api.djc8888.cn"
#define ALiAppKey    @"333513654"
#define ALiAppSecert @"3fdd69016ac040879266c02982cf914a"

#define default_contentPrefix [NSString stringWithFormat:@"https://api.djc8888.cn"]
#define default_hqPrefix   [NSString stringWithFormat:@"https://hq.djc8888.cn"]
#define default_sharePrefix   [NSString stringWithFormat:@"https://api.djc8888.cn"]
#define default_emasPrefix [NSString stringWithFormat:@"https://emas.djc8888.cn"]

#define WXMiniProgramUserName   @"gh_45d3bd5fd2c8"
#define WXMiniProgramAPPID   @"wx46c3a71ab386d2bc"
#define MQTTTopic            @"DJC_MSG_TEST"
#define MQTTGroupID          @"GID_DJC_MSG_TEST"

#define  PushAPPID    @"1"

#else
// 线上发布
#define kDakaInit    @"https://web.djc8888.com"
#define ALiAppKey    @"333513657"
#define ALiAppSecert @"91edfea3c9f94005973233ca4da44e66"

#define default_contentPrefix [NSString stringWithFormat:@"https://api.djc8888.com"]
#define default_hqPrefix   [NSString stringWithFormat:@"https://stock.djc8888.com"]
#define default_sharePrefix   [NSString stringWithFormat:@"https://web.djc8888.com"]
#define default_emasPrefix [NSString stringWithFormat:@"https://emas.djc8888.com"]

#define WXMiniProgramUserName   @"gh_75c98df157b0"
#define WXMiniProgramAPPID   @"wx65c6d302d80a8e2c"
#define MQTTTopic            @"DJC_MSG"
#define MQTTGroupID          @"GID_DJC_MSG"

#define  PushAPPID    @"1887699743603499010"

#endif



#if DEBUG
    #define FMLog(...) NSLog(__VA_ARGS__)
#else
    #define FMLog(...)
#endif

#define UNIVERSAL_LINK @"https://api.djc8888.cn/app/"
#define WXMiniProgramBindPath   @"pages/bindPhones.html"

#define kContentPrefix           @"content+"                       // 内容
#define kStockPrefix             @"stock+"                         // 行情
#define kEmasPrefix              @"emas+"                          // 推送

#define Login_URL           @"qcyzt://login"         // 登录页跳转统一使用约定协议
#define VIPCenter_URL       @"qcyzt://vipcenter"     // VIP用户中心跳转统一使用约定协议

#define KDakaInitUrl(...) [kDakaInit stringByAppendingFormat:__VA_ARGS__]
#define kDakaHqURL(...) [kStockPrefix stringByAppendingFormat:__VA_ARGS__]
#define kDakaEmasURL(...) [kEmasPrefix stringByAppendingFormat:__VA_ARGS__]
#define KDakaBaseUrl(...) [kContentPrefix stringByAppendingFormat:__VA_ARGS__]


// ------ 开放平台AppID （注：微信开放平台的appID通过init接口获取）-------
#define kDeviceType                 @"0"                           // 设备类型 0=ios 1=android 2=winphone 3=other
#define App_AppStoreId              @"1584101793"                                     // App Store id
// 阿里云一键登录
#define PNSATAUTHSDKINFO @"rBju4oLUEmH/kS+K4jlhrJWgC+iYAYroEoYQJCvN08q7/x9Q8Ot4ri2NEfcnfVet4ioXJahT8O4P2chFNi28UP2i1hYn4zFjtHSBuP7DSgt8zbELZD6zGYreR04b7j00/abyy+Dw0BJKoc8oJqQeYVbDLYscrBLXpP1JMsqBbR9VPJM6yp09/UpvgoXql8U1ISUyDD9fTFg9/wfcrpueNbaSEIG6E3fucMvaYtBqAQIzrMNxHk3faA9xmEYqacFZDZppQxah6k4="
#define BuglyId @"29945051e2"

#define TPNSAPPID  1680019898
#define TPNSAPPKey  @"ITMF17PH2FGT"

// --------------------  初始化调用  --------------------
#define AppInit_Key              @"APPCONF"                              // 初始化配置数据
#define AppInit_Task             @"AppInitTask"                         // 初始化任务配置
#define AppInit_LoginAppid       @"LoginAppid"                           // 微信开放平台appid（登录）
#define AppInit_PayAppid         @"PayAppid"                             // 微信开放平台appid（支付）
#define AppInit_QYWXID           @"kfCorpId"                             // 企业微信id
#define AppInit_QYWXServiceUrl   @"kfUrl"                                // 企业微信客服链接
#define AppInit_contentPrefix    @"contentPrefix"                        // 内容接口域名
#define AppInit_ShareUrlPrefix   @"sharePrefix"                          // 分享域名
#define AppInit_HqPrefix         @"hqPrefix"                             // 行情接口域名
#define AppInit_EmasPrefix       @"emasPrefix"                             // 推送接口域名
#define AppInit_ContactUs_QQ     @"contactUs_QQ"                         // 联系我们客服QQ
#define AppInit_ContactUs_Phone  @"contactUs_Phone"                      // 联系我们电话
#define AppInit_Complaint_Phone  @"complaint_Phone"                      // 反馈电话
#define AppInit_Quick_Comment    @"quick_Comment"                        // 快捷回复
#define AppInit_FirstRecharge_Text    @"firstRechargeText"               // 首充文案
#define AppInit_FirstRecharge_URL    @"firstRechargeURL"                 // 首充图片链接
#define AppInit_VersionUpdate         @"appVersionUpdate"                // 版本更新

#define App_lunchImageBase64Data @"lunchImageUrl"                        // 启动页面广告
#define App_lunchImageAction     @"lunchImageAction"                     // 启动页面广告点击事件
#define App_lunchImageExpireTime @"lunchImageExpireTime"                 // 启动页面广告过期时间
#define App_homeAd               @"homeAd"                               // 首页广告（包含image，action，createTime，expireTime）

#define App_User_CanScreenshot              @"App_User_CanScreenshot"    // 是否可以在某些页面截图


//--------------------  上传  --------------------
#define kAPI_System_Uplaod               KDakaBaseUrl(@"/api/system/upload.do")


//--------------------  H5 web页面地址  --------------------
#define kAPI_Question_TWGZ          (@"/api/stock/info/page.htm?name=twgz")                 // 提问规则
#define kAPI_UserCenter_LQJL        (@"/api/stock/info/pagehtm.htm?name=nxng")              // 领取奖励
#define kAPI_UserCenter_MZSM        (@"/mzsm.html")                 // 免责声明
#define kAPI_UserCenter_TGBSM       (@"/gyjb.html")                // 金币说明
#define kAPI_UserCenter_SYSC        (@"/sysc.html")                 // 使用手册
#define kAPI_Recharge_FWXY          (@"/riskProtocol.html")        // 服务协议
#define kAPI_Recharge_FXTSS         (@"/fxjss.html" )      // 风险提示书
#define kAPI_Recharge_GMFWXY        (@"/gmxy.html" )      // 产品购买服务协议
#define kAPI_RiskEvalua_FXCP        (@"/m/app/cp")              // 风险测评
#define kAPI_UserCenter_CPDJ        (@"/m/app/cpResult")                                        // 测评等级
#define kAPI_UserCenter_QSYSM       (@"/api/stock/info/page.htm?name=qsm")                  // 券使用说明
#define kAPI_SimGame_RewaedIntro    (@"/api/stock/info/pagehtm.htm?name=cgds")              // 模拟炒股大赛奖励介绍
#define kAPI_UserCenter_YSZC        (@"/yszc.html")                                         // 隐私政策
#define kAPI_UserCenter_ZCFWXY      (@"/zcxy.html")                                         // 注册服务协议
#define kAPI_UserCenter_GRXXSJQD    (@"/sjqd.html")                                         // 个人信息收集清单
#define kAPI_UserCenter_DSFSDK      (@"/yszc.html#sdk")                                     // 第三方SDK
#define kAPI_UserCenter_YYQXSM      (@"/yszc.html#yyqx")                                    // 应用权限说明
#define kAPI_UserCenter_WDDD        (@"/m/app/order/list")                                      // 我的订单
#define kAPI_UserCenter_TFJL        (@"/m/app/order/refund")                                    // 退费记录
#define kAPI_UserCenter_TGZZGS      (@"/m/tgzz")                                            // 投顾资质公示
#define kAPI_UserCenter_FLZX        (@"/m/app/active/rwdb/rwdbActive")                          // 赚金币、福利中心
#define kAPI_UserCenter_Download    (@"/m/download")                                        // 下载App链接
#define kAPI_UserCenter_Problem     (@"/m/app/commonProblem")                                   // 常见问题
#define kAPI_UserCenter_DHM         (@"/m/app/active/redeemCode")                               // 兑换码
#define kAPI_MemberCenter_Sign      (@"/m/adviser-contract/qrfwxy?orderNo=")                // 投顾会员签约地址
#define kAPI_MemberCenter_JGGD      (@"/m/market/researchReport/list")                // 机构观点
#define kAPI_MemberCenter_GSYJ_ReportDetail      (@"/m/app/active/gsyj/bgDetail")                // 股市赢家报告详情
#define kAPI_MemberCenter_GSYJDZ_ReportDetail      (@"/m/app/active/gsyjCustomized/bgDetail")                // 股市赢家定制报告详情

#define kAPI_Note_Edit              (@"/m/editor/modify?noteId=")   // 编辑笔记
#define kAPI_DakaNote_Reward              (@"/m/editor/modify?type=6&quoteId=")   // 新增动态(带转发快讯)
#define kAPI_Topic_Note(topicId,title)    [NSString stringWithFormat:@"/m/editor/modify?type=6&topicId=%@&topicTitle=%@", topicId, title]    // 话题新建笔记6动态
#define kAPI_Note_New               (@"/m/editor/modify?type=")     // 新建笔记1长文 6动态
#define kAPI_Draft_List               (@"/m/editor/drafts")     // 草稿列表
#define kAPI_Stock_Monitor              (@"/m/market/monitoring?stockCode=")     // 个股监控

#define YCkStockTradeHomeUrl(params) [NSString stringWithFormat:@"%@%@#/index", YCHomeUrl, params] // 股票交易首页


////--------------------  恒生聚源   --------------------
//#define KAPI_HengSheng_SelfStock_NewsURL      (@"/mobileF10View/news.html")               // 个股新闻
//#define KAPI_HengSheng_SelfSelectStock_AllURL (@"/mobileF10View/selfselect_list.html")    // 自选股消息

/// ----------------优品获取股票详情请求tag标记------------------
// 请求市场状态
#define FMMarketStatusTag 50000
// 首页行情请求标记
#define FMCommunityhqTag 50001
// 大盘竞猜行情请求标记
#define FMQuizhqTag 50002
// 自选股上面获取指数请求标记
#define FMSelfStockIndexHqTag 50003
// 笔记相关股票
#define FMRelatedStockhqTag 50004
// 笔记提及股票
#define FMMentionStockhqTag 50005
// 快讯相关股票
#define FMNewsRelatedStockhqTag 50006
// VIP股票池
#define FMMemberCenterStockPoolTag 50007
// 自选股
#define FMSelfStockHqTag 50008
// 自选股公告研报,实际占用了 50010，50011
#define FMSelfStockNoticeReportHqTag 50009

// 板块相关
/// 板块成分股, 实际占用了 50012，50013, 50014
#define FMBlockConstituentStocksTag 50012
/// 股票所属概念
#define FMBlockStockConceptsTag 50015
/// 50016-50018被占用，写在UPMarket2中
/// 板块资金流向
#define FMBlockFundFlowFundTag 50019
/// 板块列表
#define FMBlockListTag 50020

// 第一创业
//OCR授权码
#define AuthCodeString @"MzMwOTEzbm9kZXZpY2Vjd2F1dGhvcml6ZZ/k5OXi5+bq/+bg5efm4+f+5uXi4Obg5Yjm5uvl5ubrkeXm5uvl5uai6+Xm5uvl5uTm6+Xm5uDm1efr5+vn6+er4Ofr5+vn6/vn5+bm5Ofi"

#define KHDEV 0

#if KHDEV
#define YCHomeUrl @"https://h5tradetest.95358.com:8444/dist/src/trade-entry/index.html?"
#define YCBuySellUrl @"https://h5tradetest.95358.com:8444/index.html?"
#define YCKaiHuUrl @"https://tctest.fcsc.com:9008/fcsc/acct4/index.html?or=DJC&bn=1026&ocr=1&av=10&color=d70008"
#else
#define YCHomeUrl @"https://h5hqservice.95358.com:8443/dist/src/trade-entry/index.html?"
#define YCBuySellUrl @"https://h5hqservice.95358.com:8443/index.html?"
#define YCKaiHuUrl @"https://kh.fcsc.com/fcsc/acct4/login.html?or=SCDJC&bn=1026&ocr=1&av=10&color=d70008"
#endif

#define YCkStockTradeHomeUrl(params) [NSString stringWithFormat:@"%@%@#/index", YCHomeUrl, params] // 股票交易首页
#define YCkStockTradeBuySellUrl(params, tab) [NSString stringWithFormat:@"%@?%@#!/deal/stock?tab=%zd", YCBuySellUrl, params, tab]  // 买入卖出持仓

//一创提供的资讯接口
#define YCHostURL      @"https://ggzx.95358.com:7615"
#define YCNewsHomeURL  [NSString stringWithFormat:@"%@/TQLEX?Entry=CWServ.SecuInfo",YCHostURL]
#endif /* FMGlobalRequestInterface_h */


