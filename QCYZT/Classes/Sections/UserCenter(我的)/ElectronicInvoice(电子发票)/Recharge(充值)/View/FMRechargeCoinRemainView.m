//
//  FMRechargeCoinRemainView.m
//  QCYZT
//
//  Created by Max on 2023/4/6.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMRechargeCoinRemainView.h"
#import "YTGNormalWebVC.h"

@interface FMRechargeCoinRemainView ()
/// 图标
@property(nonatomic,strong) UIImageView *iconimgV;
/// 金币余额：
@property(nonatomic,strong) UILabel *remainDescribeLab;
@end

@implementation FMRechargeCoinRemainView


-(instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self commonInit];
    }
    return self;
};
-(instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self commonInit];
    }
    return self;
};

-(void)commonInit{
    UI_View_Radius(self, 5);
    
    [self addSubview:self.iconimgV];
    [self addSubview:self.remainDescribeLab];
    [self addSubview:self.remainCountLab];
    [self addSubview:self.taskBtn];
    
    CGFloat padding = 10;
    [self.iconimgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(padding);
        make.centerY.mas_equalTo(self);
    }];
    
    CGFloat remainDescribeLabMarginLeft = 5;
    [self.remainDescribeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.iconimgV.mas_right).offset(remainDescribeLabMarginLeft);
        make.centerY.mas_equalTo(self.iconimgV);
    }];
    
    [self.remainCountLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.remainDescribeLab.mas_right).offset(0);
        make.centerY.mas_equalTo(self.iconimgV);
        make.right.lessThanOrEqualTo(self).offset(-padding);
    }];
    
    [self.taskBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(0);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self.taskBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];
    self.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfff5eb), ColorWithHex(0xfff2f2)] withFrame:self.bounds direction:GradientDirectionLeftToRight];
}

- (void)taskBtnClick {
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
    }];
}

-(UIImageView *)iconimgV{
    if(!_iconimgV){
        _iconimgV = [[UIImageView alloc] init];
        UIImage *img = [UIImage imageNamed:@"coins_circle"];
        _iconimgV.image = img;
    }
    return _iconimgV;
}

-(UILabel *)remainDescribeLab{
    if(!_remainDescribeLab){
        _remainDescribeLab = [UILabel new];
        _remainDescribeLab.text = @"金币余额：";
        _remainDescribeLab.textColor = ColorWithHex(0x333333);
        _remainDescribeLab.font = FontWithSize(16);
    }
    return _remainDescribeLab;
}

-(UILabel *)remainCountLab{
    if(!_remainCountLab){
        _remainCountLab = [UILabel new];
        _remainCountLab.textColor = ColorWithHex(0x333333);
        _remainCountLab.font = BoldFontWithSize(18);
    }
    return _remainCountLab;
}

- (UIButton *)taskBtn {
    if (!_taskBtn) {
        _taskBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:FMNavColor backgroundColor:FMClearColor title:@"做任务，赚积分" image:ImageWithName(@"daka_operation_arrow") target:self action:@selector(taskBtnClick)];
    }
    
    return _taskBtn;
}


@end
