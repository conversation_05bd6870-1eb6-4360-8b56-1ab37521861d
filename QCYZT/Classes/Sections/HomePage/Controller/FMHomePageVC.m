//
//  FMHomePageVC.m
//  QCYZT
//  首页(初始版本称为社区)
//  Created by shumi on 2022/6/16.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMHomePageVC.h"
#import "HttpRequestTool+DailyTask.h"
#import "FMTaskConfigModel.h"
#import "FMMsgRequestAllDataStaus.h"
#import "FMOuterTableView.h"
#import "FMHomeRequestManager.h"
#import "FMHomeDataSource.h"
#import "FMHomePageNavView.h"
#import "FMHomePageBottomFloatView.h"
#import "FMHomePageFocusVC.h"
#import "FMHomePageRecommendedVC.h"
#import "FMHomePageNoteVC.h"
#import "FMHomePageAskCodeVC.h"
#import "FMHomePageVideoVC.h"
#import "NSObject+FBKVOController.h"
#import "FMHomepageLoginReminderView.h"

@interface FMHomePageVC ()<UITableViewDelegate, UITableViewDataSource, SGPageTitleViewDelegate, SGPageContentCollectionViewDelegate, FMInnerTableVCDelegate>

/// 导航栏View
@property (nonatomic, strong) FMHomePageNavView *navView;
/// 底部浮动按钮
@property (nonatomic, strong) FMHomePageBottomFloatView *floatActionStackV;

/// 主题结构
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, assign) NSInteger previousSelectedIndex;
@property (nonatomic, strong) SGPageContentCollectionView *contentCollectionView;
@property (nonatomic, strong) FMOuterTableView *tableView;
@property (nonatomic, strong) UITableViewCell *contentCell;
@property (nonatomic, weak) FMInnerTableViewController *showingVC;

/// 请求处理类
@property (nonatomic, strong) FMHomeRequestManager *requestManager;
/// 数据处理类
@property (nonatomic, strong) FMHomeDataSource *dataManager;
/// 是否正在被拖拽
@property(nonatomic,assign) BOOL dragged;
/// 是否注册了通知
@property (nonatomic, assign) BOOL isRegisterNotification;

// 首页新手任务页面浏览计时
/// 计时数
@property (nonatomic, assign) NSInteger pageBrowseTime;
/// 是否需要增加计数标志，1表示需要，2表示当前不需要，0表示不再需要，这里需要三个状态。
@property (nonatomic, assign) NSInteger needAddTimeCountFlag;

@property (nonatomic, assign) BOOL needsRefresh;

@property (nonatomic, strong) UPMarketMonitor *marketMonitor;

@property (nonatomic, strong) FMHomepageLoginReminderView *loginReminderView;


@end

@implementation FMHomePageVC

#pragma mark- 生命周期
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    // 设置UI
    [self setupUI];
    
    // 加载缓存
    [self.requestManager loadCacheDataWithBlock:^{
        [self.tableView reloadData];
    }];
    
    [self getHomeSmallBtnData];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(headerAction) name:kCommunityPageRefreshData object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appInit) name:kAppInitDataRefresh object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(statusBarOrientationChanged:) name:UIApplicationDidChangeStatusBarOrientationNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];

    self.needAddTimeCountFlag = 0;
    
    [self beginMonitor];
}

// 启动一下定时器，让新建的定时器能快速启动
- (void)beginMonitor {
    UPMarketType2StockReq *req = [[UPMarketType2StockReq alloc] init];
    [self.marketMonitor startMonitorStockByType:req tag:0 completionHandler:^(UPMarketType2StockRsp *rsp, NSError *error) {
        NSLog(@"筛选--板块定时器启动一下");
        [self.marketMonitor stopMonitor];
    }];
}

- (void)statusBarOrientationChanged:(NSNotification *)noti {
    [self.tableView reloadData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

    // 重置可能残留的transform，防止UI异常放大
    self.view.transform = CGAffineTransformIdentity;
    self.tableView.transform = CGAffineTransformIdentity;

    // 确保window和根视图也重置transform
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    keyWindow.transform = CGAffineTransformIdentity;
    keyWindow.rootViewController.view.transform = CGAffineTransformIdentity;

    [self.navView updateUserCenterBtn];

    [self.floatActionStackV mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.floatActionStackV.floatActionBtnRightMargin_show);
    }];
    [self.floatActionStackV layoutIfNeeded];

    [self.floatActionStackV updateBottomFloatView];

    if (self.needsRefresh) {
        self.needsRefresh = NO;

        [self getHomeSmallBtnData];
        [self headerAction];
    }

    if (![FMHelper isLogined] && self.loginReminderView.superview) {
        self.loginReminderView.hidden = NO;
    } else {
        self.loginReminderView.hidden = YES;
    }

    [self configNavRedColor];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    dispatch_async(dispatch_get_main_queue(), ^{
        [FMAppDelegate shareApp].main.myTabBar.hidden = NO;
    });
}

- (void)addNotification {
    if (!self.isRegisterNotification) {
        [FMHelper addLoginAndLogoutNotificationWithObserver:self  selector:@selector(handleLoginStatusNotification) monitorAuthLogin:YES];
        // 进入后台
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
        // 发布动态长文成功通知
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(publishContentSuccess:) name:kPublishContentSuccess object:nil];
        // 无网到有网的通知
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkChangedOnline:) name:kNetworkStatusChangedToOnlineNotification object:nil];
        
        self.isRegisterNotification = YES;
    }
}

/// 设置UI
- (void)setupUI {
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(CGSizeMake(UI_SCREEN_WIDTH - 30, 44)); // 宽度无法达到UI_SCREEN_WIDTH - 30，x是16, 原因待查
    }];
    
    UIImageView *topbackImg = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 75)];
    topbackImg.image = FMImgInBundle(@"首页/首页顶部");
    topbackImg.userInteractionEnabled = YES;
    [self.view addSubview:topbackImg];
    
    [self configPageTitle];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(10, 0, 0, 0));
    }];
    
    [self.view addSubview:self.floatActionStackV];
    [self.floatActionStackV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.floatActionStackV.floatActionBtnRightMargin_show);
        make.bottom.equalTo(@-20);
    }];
    
    [self.view addSubview:self.loginReminderView];
    [self.loginReminderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(-10);
        make.height.equalTo(40);
    }];
    self.loginReminderView.hidden = YES;
}

/// 配置选项卡
- (void)configPageTitle {
    [self.pageTitleView removeFromSuperview];
    self.pageTitleView = nil;
    [self.contentCollectionView removeFromSuperview];
    self.contentCollectionView = nil;
    
    NSArray *titles = @[@"关注", @"推荐", @"笔记", @"问股", @"视频"];
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES; //是否均匀分布
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.up_dividerColor;
    SGPageTitleView *pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SegmentControl_Height) delegate:self titleNames:titles configure:configure];
    pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:pageTitleView];
    self.pageTitleView = pageTitleView;
        
    CGFloat collectionHeight = UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SegmentControl_Height - UI_TABBAR_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 10;
    SGPageContentCollectionView *contentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, collectionHeight) parentVC:self childVCs:[self getChildVCs]];
    contentCollectionView.backgroundColor = FMClearColor;
    contentCollectionView.collectionView.backgroundColor = FMClearColor;
    self.contentCollectionView = contentCollectionView;
    self.contentCollectionView.delegatePageContentCollectionView = self;
    [self.contentCell.contentView addSubview:self.contentCollectionView];
    self.pageTitleView.selectedIndex = 1;
}

- (NSArray *)getChildVCs {
    FMHomePageFocusVC *vc = [[FMHomePageFocusVC alloc] init];
    vc.delegate = self;
    [self addChildViewController:vc];
    
    FMHomePageRecommendedVC *recommendedVC = [[FMHomePageRecommendedVC alloc] init];
    recommendedVC.delegate = self;
    [self addChildViewController:recommendedVC];
    
    FMHomePageNoteVC *noteVC = [[FMHomePageNoteVC alloc] init];
    noteVC.delegate = self;
    [self addChildViewController:noteVC];

    FMHomePageAskCodeVC *askCodeVC = [[FMHomePageAskCodeVC alloc] init];
    askCodeVC.delegate = self;
    [self addChildViewController:askCodeVC];
    
    FMHomePageVideoVC *viedoVC = [[FMHomePageVideoVC alloc] init];
    viedoVC.delegate = self;
    [self addChildViewController:viedoVC];
    
    for (FMInnerTableViewController *vc  in self.childViewControllers) {
        WEAKSELF
        vc.tableScrollUp = ^(CGFloat offsetY) {
            [__weakSelf scrollUp];
        };
        vc.tableScrollDown =  ^(CGFloat offsetY){
            [__weakSelf scrollDown];
        };
        vc.delegate = self;
    }
    
    return self.childViewControllers;
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    self.showingVC = self.childViewControllers[selectedIndex];
    [self.contentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];

    if (self.previousSelectedIndex != selectedIndex) {
        CGFloat topCellOffset = [self.tableView rectForSection:self.requestManager.dataArray.count].origin.y;
        CGFloat offSetY = self.tableView.contentOffset.y;
        if (offSetY < topCellOffset) { // 不加此判断，在iPhone14Pro上吸顶后点击切换无法保持子VC的偏移
            [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:self.dataManager.dataArray.count] atScrollPosition:UITableViewScrollPositionTop animated:NO];
        }
    }
    
    self.previousSelectedIndex = selectedIndex;
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    self.showingVC = self.childViewControllers[targetIndex];
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - TableView Delegate/Datasource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.requestManager.dataArray.count + 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section < self.requestManager.dataArray.count) {
        return [self.dataManager tableView:tableView cellAtIndexPath:indexPath];
    }
    return self.contentCell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section < self.requestManager.dataArray.count) {
        return [self.dataManager cellHeightWithTableView:tableView cellAtIndexPath:indexPath];
    }
    return UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SegmentControl_Height - UI_TABBAR_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 10;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section < self.requestManager.dataArray.count) {
        UIView *view = [UIView new];
        view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        return view;
    }
    return self.pageTitleView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section < self.requestManager.dataArray.count) {
        if (section == 2) {
            NSArray *arr1 = self.requestManager.dataArray[1];
            BOOL arr1hasData = NO;
            for (NSArray *arr in arr1) {
                if (arr.count) {
                    arr1hasData = YES;
                    break;
                }
            }
            NSArray *arr2 = self.requestManager.dataArray[2];
            if (!arr2.count && arr1hasData) {
                return 8;
            }
        }
        return CGFLOAT_MIN;
    }
    return UI_SegmentControl_Height;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - UIScrollView Delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat topCellOffset = [self.tableView rectForSection:self.requestManager.dataArray.count].origin.y;
        
    // 如果里层tableView的偏移量大于0，将外层tableView的偏移量定在tableTopViewHeight，保持悬停
    if (self.showingVC.tableView.contentOffset.y > 0) {
        self.tableView.contentOffset = CGPointMake(0, topCellOffset);
    }
    
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），发出通知让每个子tableView的偏移量变成0
    CGFloat offSetY = self.tableView.contentOffset.y;
    if (offSetY < topCellOffset) {
        for (FMInnerTableViewController *VC in self.childViewControllers) {
            VC.tableView.contentOffset = CGPointZero;
        }
    }
    
    if (!_dragged) {
        return;
    }
    CGPoint point = [scrollView.panGestureRecognizer translationInView:self.view];
    if (point.y > 0) {
        [self scrollDown];
    } else {
        [self scrollUp];
    }
}

- (void)scrollUp {
    [self.floatActionStackV mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.floatActionStackV.floatActionBtnRightMargin_hide);
    }];
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1 animations:^{
            self.floatActionStackV.frame = CGRectMake(UI_SCREEN_WIDTH - self.floatActionStackV.width + self.floatActionStackV.floatActionBtnRightMargin_hide, self.floatActionStackV.origin.y, self.floatActionStackV.width, self.floatActionStackV.height);
        } completion:^(BOOL finished) {
        }];
    });
}

- (void)scrollDown {
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1 animations:^{
            self.floatActionStackV.frame = CGRectMake(UI_SCREEN_WIDTH - self.floatActionStackV.width + self.floatActionStackV.floatActionBtnRightMargin_show, self.floatActionStackV.origin.y, self.floatActionStackV.width, self.floatActionStackV.height);
        } completion:^(BOOL finished) {
        }];
    });
}

-(void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    _dragged = true;
}

#pragma mark - FMInnerTableVCDelegate
- (void)innerTableVCTableviewScroll:(UITableView *)innerTableview {
    CGFloat tableTopViewHeight = ([self.tableView rectForSection:self.requestManager.dataArray.count].origin.y);
    // 如果外层tableView偏移量小于tableTopViewHeight（也就是头部视图正在显示），将子tableView的偏移量变成0
    if (self.tableView.contentOffset.y < tableTopViewHeight) {
        innerTableview.contentOffset = CGPointZero;
        innerTableview.showsVerticalScrollIndicator = NO;
    } else {
        innerTableview.showsVerticalScrollIndicator = YES;
    }
}

#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)networkChangedOnline:(NSNotification *)noti {
    [self getHomeSmallBtnData];
    [self headerAction];
}

/// 设置首页右下方悬浮按钮
- (void)getHomeSmallBtnData {
    NSLog(@"请求悬浮按钮");
    [self.floatActionStackV requestBtnsWithSuccessBlock:^(HomeSmallButtonsModel *topModel) {
        if (topModel) {
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.contentMode = UIViewContentModeScaleAspectFit;
            imageView.userInteractionEnabled = YES;
            [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:topModel.icon] options:0 progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
                if (image) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        UIImage *currentImg = [image cropImageToSize:CGSizeMake(image.size.width / image.size.height * 30 , 30)];
                        imageView.size = currentImg.size;
                        imageView.image = currentImg;
                        [self.navView changeRightSubView:imageView];
                    });
                }
            }];
            imageView.userInteractionEnabled = YES;
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
                [ProtocolJump jumpWithUrl:topModel.action];
            }];
            [imageView addGestureRecognizer:tap];
        } else {
            [self.navView changeRightSubView:nil];
        }
    }];
}

/// 进入后台
- (void)didEnterForeground {
    if ([FMUserDefault getUserId].length) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 此处必须延时，保证APP后台情况下，点击通知栏启动APP时，更新消息读取状态 isRead接口先被调用
            [FMMsgRequestAllDataStaus requestMsgUnreadDataWithBlock:^{
            }];
        });
    }
}

/// 发布内容成功的通知(定位到关注)
- (void)publishContentSuccess:(NSNotification *)noti {
    [self pageTitleView:self.pageTitleView selectedIndex:0];
}

- (void)appWillResignActive {
    [self pauseTask];
}

- (void)appDidBecomeActive {
    [self resumeTask];
    [self getHomeSmallBtnData];
}

#pragma mark - Event
- (void)headerAction {
    [self.requestManager requestDataforTableView:self.tableView WithParam:nil SuccessBlock:^(BOOL noMoreData, BOOL isContentRequest) {
        [self.tableView reloadData];
    } FailureBlock:^(NSString * _Nonnull errmessage) {
    }];
    WEAKSELF
    self.requestManager.requestResultBlock = ^(MultipleRequestResult requestResult) {
        // 注册通知
        [__weakSelf addNotification];
        [__weakSelf.tableView.mj_header endRefreshing];
    };
    
    if (self.showingVC) {
        [self.showingVC headerAction];
    }
}

- (void)appInit {
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    BOOL showStrategy = [initDic[@"strategyTag"] boolValue];
    self.requestManager.showStrategyColumn = showStrategy;
    
    [self.tableView.mj_header beginRefreshing];
}

#pragma mark - Public
- (void)beginTask {
    self.needAddTimeCountFlag = 1;
    
    WEAKSELF
    [self.KVOController observe:[CountDownShareInstance shareInstance] keyPath:DifferentValue options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([change objectForKey:NSKeyValueChangeNewKey] && __weakSelf.needAddTimeCountFlag == 1) {
            __weakSelf.pageBrowseTime ++;
            FMLog(@"首页浏览时长%zd秒", __weakSelf.pageBrowseTime);
            FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
            // 此处readNoteTask需修改
            if (!taskConfig.taskDic[@(FMTaskTypeReadNote)].isEnable) {
                return;
            }
            if (__weakSelf.pageBrowseTime >= taskConfig.taskDic[@(FMTaskTypeBrowseHomepage)].taskRequire) {
                __weakSelf.needAddTimeCountFlag = 0;
                // 发起完成请求
                [HttpRequestTool requestCompleteBrowseHomepageTaskWithStart:^{
                } failure:^{
                } success:^(NSDictionary *dic) {
                    if ([dic[@"status"] isEqualToString:@"1"]) {
                        FMTaskConfigModel *userTaskProgress = [FMUserDefault getUnArchiverDataForKey:UserTaskProgressCacheKey];
                        [PushMessageView showWithTitle:@"任务完成" message:[NSString stringWithFormat:@"浏览首页达成！额外赠送%zd金币", userTaskProgress.taskDic[@(FMTaskTypeBrowseHomepage)].taskAward] noticeImage:nil sureTitle:@"更多任务" cancelTitle:@"确定" clickSure:^{
                            [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                                [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
                            }];
                        } clickCancel:^{
                        }];
                    }
                }];
            }
        }
    }];
}

- (void)pauseTask {
    if (self.needAddTimeCountFlag == 1) {
        self.needAddTimeCountFlag = 2;
    }
}

- (void)resumeTask {
    if (self.needAddTimeCountFlag == 2) {
        self.needAddTimeCountFlag = 1;
    }
}

#pragma mark - lazy
- (FMHomePageNavView *)navView {
    if (!_navView) {
        _navView = [[FMHomePageNavView alloc] init];
    }
    
    return _navView;
}

- (FMHomePageBottomFloatView *)floatActionStackV {
    if (!_floatActionStackV) {
        _floatActionStackV = [[FMHomePageBottomFloatView alloc] init];
    }
    
    return _floatActionStackV;
}

- (FMOuterTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMOuterTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = FMClearColor;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.estimatedRowHeight = 0;
        _tableView.estimatedSectionHeaderHeight = 0;
        _tableView.estimatedSectionFooterHeight = 0;
        _tableView.separatorColor = UIColor.up_dividerColor;
        [self.dataManager registerCell:_tableView];
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            [self headerAction];
        }];
    }
    return _tableView;
}

- (UITableViewCell *)contentCell {
    if (!_contentCell) {
        UITableViewCell *cell = [[UITableViewCell alloc] init];
        cell.backgroundColor = cell.contentView.backgroundColor = UIColor.up_contentBgColor;
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        _contentCell = cell;
    }
    
    return _contentCell;
}

- (FMHomepageLoginReminderView *)loginReminderView {
    if (!_loginReminderView) {
        _loginReminderView = [[FMHomepageLoginReminderView alloc] init];
    }
    
    return _loginReminderView;
}

- (void)setIndex:(NSInteger)index {
    _index = index;
    
    if (self.view) { // 进入到viewDidLoad
        self.pageTitleView.selectedIndex = index;
        [self.contentCollectionView setPageContentCollectionViewCurrentIndex:index];
    }
}

- (FMHomeRequestManager *)requestManager {
    if (!_requestManager) {
        _requestManager = [[FMHomeRequestManager alloc] init];
        NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
        BOOL showStrategy = [initDic[@"strategyTag"] boolValue];
        _requestManager.showStrategyColumn = showStrategy;
    }
    return _requestManager;
}

- (FMHomeDataSource *)dataManager {
    if (!_dataManager) {
        _dataManager = [[FMHomeDataSource alloc] init];
        _dataManager.dataArray = self.requestManager.dataArray;
    }
    return _dataManager;
}

- (UPMarketMonitor *)marketMonitor {
    if (!_marketMonitor) {
        _marketMonitor = [UPMarketMonitor monitorWithInterval:1];
    }
    return _marketMonitor;
}

@end
