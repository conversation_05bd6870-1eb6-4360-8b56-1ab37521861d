//
//  UPMarket2SDITodayMainFlowIntoView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/24.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDITodayMainFlowIntoView.h"
#import "UPMarket2SDIStockHistogramView.h"
#import "UPMarket2StockSettingAlertView.h"
#import "UPMarket2StockUIUtil.h"
#import "FMUPDataTool.h"

@interface UPMarket2SDITodayMainFlowIntoView ()

@property (nonatomic, strong) UILabel *titleLable;  // 标题
@property (nonatomic, strong) UIButton *flowintoBtn;   // 资金流向说明按钮

@property (nonatomic, strong) UIView *intoBackView;   // 信息的背景
@property (nonatomic, strong) UIView *intoLeftView;
@property (nonatomic, strong) UPErrorView *errorView;

//@property (nonatomic, strong) UIView *intoMark;   // 流入标识
//@property (nonatomic, strong) UILabel *intoLabel; // 流入文字
//@property (nonatomic, strong) UIView *outMark;    // 流出标识
//@property (nonatomic, strong) UILabel *outLabel;  // 流出文字
//@property (nonatomic, strong) UILabel *unitLabel;  // 单位


@property (nonatomic, strong) UILabel *mInTitleLabel; // 主力流入 标题
@property (nonatomic, strong) UILabel *mInValueLabel; // 主力流入 值
@property (nonatomic, strong) UILabel *mOutTitleLabel; // 主力流出 标题
@property (nonatomic, strong) UILabel *mOutValueLabel; // 主力流出 值
@property (nonatomic, strong) UILabel *mJInTitleLabel; // 主力净流入 标题
@property (nonatomic, strong) UILabel *mJInValueLabel; // 主力净流入 值

@property (nonatomic, strong) UPMarket2SDIStockHistogramView *histogramView;

@property (nonatomic, strong) UPHqCapitalFlowData *capitalFlowData;
//@property (nonatomic, strong) NSDictionary *capitalFlowDic;


@end

@implementation UPMarket2SDITodayMainFlowIntoView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViews];
        [self setConstraints];
    }
    return self;
}

- (void)setupViews {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    
    [self addSubview:self.titleLable];
    [self addSubview:self.flowintoBtn];
    [self addSubview:self.intoBackView];
    [self addSubview:self.errorView];
    
//    [self.intoBackView addSubview:self.intoMark];
//    [self.intoBackView addSubview:self.intoLabel];
//    [self.intoBackView addSubview:self.outMark];
//    [self.intoBackView addSubview:self.outLabel];
//    [self.intoBackView addSubview:self.unitLabel];
    
    UIView *leftView = [UIView new];
    self.intoLeftView = leftView;
    [self.intoBackView addSubview:self.intoLeftView];
    
    
    [self.intoLeftView addSubview:self.mInTitleLabel];
    [self.intoLeftView addSubview:self.mInValueLabel];
    [self.intoLeftView addSubview:self.mOutTitleLabel];
    [self.intoLeftView addSubview:self.mOutValueLabel];
    [self.intoLeftView addSubview:self.mJInTitleLabel];
    [self.intoLeftView addSubview:self.mJInValueLabel];
    
    [self.intoBackView addSubview:self.histogramView];
}

- (void)setConstraints {
    CGFloat padding_h = UPWidth(15);
    
    [self.titleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(UPWidth(25));
        make.top.equalTo(self);
        make.height.equalTo(@UPWidth(46));
    }];
    
    UIView *redView = [[UIView alloc] init];
    [self addSubview:redView];
    redView.backgroundColor = HexColor(@"#FC0002");
    [redView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(padding_h);
        make.centerY.equalTo(self.titleLable);
        make.width.equalTo(@UPWidth(3));
        make.height.equalTo(@UPWidth(12));
    }];
    redView.layer.cornerRadius = UPWidth(1.5);
    redView.layer.masksToBounds = YES;
    
    [self.flowintoBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLable.mas_right);
        make.top.equalTo(self);
        make.height.equalTo(@UPWidth(46));
        make.width.equalTo(@UPWidth(42));
    }];
    [self.intoBackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.top.equalTo(self.titleLable.mas_bottom);
    }];
    [self.errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.intoBackView);
    }];
    
//    [self.intoMark mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self.titleLable.mas_left);
//        make.top.equalTo(self.intoBackView.mas_top).offset(UPWidth(8.5));
//        make.width.height.equalTo(@UPWidth(6));
//    }];
//    [self.intoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self.intoMark.mas_right).offset(UPWidth(5));
//        make.top.equalTo(self.intoBackView.mas_top).offset(UPWidth(5));
//        make.width.equalTo(@UPWidth(30));
//        make.height.equalTo(@UPWidth(13));
//    }];
//    [self.outMark mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self.intoLabel.mas_right).offset(UPWidth(11));
//        make.top.equalTo(self.intoMark.mas_top);
//        make.width.height.equalTo(@UPWidth(6));
//    }];
//    [self.outLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.equalTo(self.outMark.mas_right).offset(UPWidth(5));
//        make.top.equalTo(self.intoLabel.mas_top);
//        make.width.height.equalTo(self.intoLabel);
//    }];
//    [self.unitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.equalTo(self).offset(-padding_h);
//        make.top.equalTo(self.intoLabel.mas_top);
//        make.width.equalTo(@UPWidth(70));
//        make.height.equalTo(self.intoLabel.mas_height);
//    }];
    
    [self.intoLeftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.top.equalTo(@0);
        make.width.equalTo(self.intoBackView.mas_width).multipliedBy(0.5);
    }];
    
    [self.mInTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(padding_h));
        make.top.equalTo(@UPWidth(13.5));
        make.height.equalTo(@UPWidth(20));
    }];
    
    [self.mInValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mInTitleLabel.mas_right).offset(UPWidth(5));
        make.top.equalTo(self.mInTitleLabel.mas_top);
        make.height.equalTo(self.mInTitleLabel.mas_height);
        make.right.equalTo(@(-padding_h));
    }];
    
    [self.mOutTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mInTitleLabel);
        make.top.equalTo(self.mInTitleLabel.mas_bottom).offset(UPWidth(26.5));
        make.height.equalTo(self.mInTitleLabel);
    }];
    
    [self.mOutValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mInValueLabel);
        make.left.equalTo(self.mOutTitleLabel.mas_right).offset(UPWidth(5));
        make.top.equalTo(self.mOutTitleLabel);
        make.height.equalTo(self.mInTitleLabel);
    }];
    
    [self.mJInTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mInTitleLabel);
        make.top.equalTo(self.mOutTitleLabel.mas_bottom).offset(UPWidth(26.5));
        make.height.equalTo(self.mInTitleLabel);
    }];
    
    [self.mJInValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mInValueLabel);
        make.left.equalTo(self.mJInTitleLabel.mas_right).offset(UPWidth(5));
        make.top.equalTo(self.mJInTitleLabel);
        make.height.equalTo(self.mInTitleLabel);
    }];
    
    UIView *sepline = [UIView new];
    sepline.backgroundColor = UIColor.up_dividerColor;
    [self.intoLeftView addSubview:sepline];
    [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.equalTo(@0);
        make.bottom.equalTo(self.mJInTitleLabel).offset(UPWidth(3));
        make.width.equalTo(@1);
    }];
    
    [self.histogramView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.intoLeftView.mas_right).offset(padding_h);
        make.right.equalTo(self).offset(-padding_h);
        make.top.equalTo(self.intoLeftView);
        make.bottom.equalTo(self.mJInTitleLabel);
    }];
}

// MARK: Private

- (void)viewDidAppear {
    [self requestData];
}

- (void)viewDidDisappear {
}

// MARK: Private
- (BOOL)isHK {
    if (UPMarketSetCodeHK == self.stockHq.setCode || UPMarketSetCodeHKI == self.stockHq.setCode || UPMarketSetCodeGGT == self.stockHq.setCode) {  // 非港股
        return YES;
    } else {
        return NO;
    }
}
- (void)showFlowIntoDetail {
    NSString *title = @"资金流向说明";
    
    UIFont *markFont = [UIFont up_boldFontOfSize:UPWidth(13)];
    UIFont *otherFont = [UIFont up_fontOfSize:UPWidth(13)];
    UIColor *markColor = UIColor.up_textPrimaryColor;
    UIColor *otherColor = UIColor.up_textSecondaryColor;
    NSDictionary *attrsMark = @{NSForegroundColorAttributeName:markColor, NSFontAttributeName: markFont};
    NSDictionary *attrsOther = @{NSForegroundColorAttributeName:otherColor, NSFontAttributeName: otherFont};    
    
    NSMutableAttributedString *aString = [[NSMutableAttributedString alloc] init];
    
    {
        // 1. 小单
        NSAttributedString *markString = [[NSAttributedString alloc] initWithString:@"1.小单" attributes:attrsMark];
        [aString appendAttributedString:markString];
        
        if ([self isHK]) {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n小于等于前20个交易日的平均每笔成交额\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        } else {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n当日单笔成交量小于2万股，或成交额小于2万元\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        }
    }
    
    {
        // 2. 中单
        NSAttributedString *markString = [[NSAttributedString alloc] initWithString:@"2.中单" attributes:attrsMark];
        [aString appendAttributedString:markString];
        
        if ([self isHK]) {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n大于前20个交易日的平均每笔成交额小于等于10倍前20个交易日的平均每笔成交额\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        } else {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n当日单笔成交量大于等于2万股小于10万股，或成交额大于等于4万元小于20万元\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        }
    }
    
    {
        // 3. 大单
        NSAttributedString *markString = [[NSAttributedString alloc] initWithString:@"3.大单" attributes:attrsMark];
        [aString appendAttributedString:markString];
        
        if ([self isHK]) {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n大于10倍前20个交易日的平均每笔成交额小于等于20倍前20个交易日的平均每笔成交额\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        } else {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n当日单笔成交量大于等于10万股小于50万股，或成交额大于等于20万元小于100万元\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        }
    }
    
    {
        // 4. 超大单
        NSAttributedString *markString = [[NSAttributedString alloc] initWithString:@"4.超大单" attributes:attrsMark];
        [aString appendAttributedString:markString];
        
        if ([self isHK]) {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n大于20倍前20个交易日的平均每笔成交额\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        } else {
            NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"\n当日单笔成交量大于等于50万股，或成交额大于等于100万元\n\n" attributes:attrsOther];
            [aString appendAttributedString:otherString];
        }
    }
    
    {
        // 解释
        NSAttributedString *otherString = [[NSAttributedString alloc] initWithString:@"[主力=大单+超大单     散户=小单+中单]" attributes:attrsOther];
        [aString appendAttributedString:otherString];
    }
    
    UPMarket2StockSettingAlertView *alertView = [UPMarket2StockSettingAlertView alertWithTitle:title attributedMessage:aString];
    alertView.alertHeight = [UPThemeManager isWidgetShouldScale:self] ? UPWidth(610) : UPWidth(460);
    [alertView showInView:[UPRouter visibleViewController].view];
}

- (void)updateNoDataView {
    double mainIn = self.capitalFlowData.superIn + self.capitalFlowData.bigIn;
    double mainOut = self.capitalFlowData.superOut + self.capitalFlowData.bigOut;
    double otherIn = self.capitalFlowData.midIn + self.capitalFlowData.smallIn;
    double otherOut = self.capitalFlowData.midOut + self.capitalFlowData.smallOut;
    double total = mainIn + mainOut + otherIn + otherOut;

    if (total) {
        [self setErrorViewHidden:YES];
        //数据
        [self updateTextLabel];

        //直方图
        [self setupHistogramView];
    } else {
        // 无数据
        [self setErrorViewHidden:NO];
    }
}

/// 直接更新文字
- (void)updateTextLabel {
    long long mainIn = self.capitalFlowData.superIn + self.capitalFlowData.bigIn;
    long long mainOut = -(self.capitalFlowData.superOut + self.capitalFlowData.bigOut);
    long long mainTotal = mainIn + mainOut;

    CGFloat unit = 10000.0;  // 万
    NSInteger precise= 0;
    self.mInValueLabel.text = [UPMarketUICalculateUtil transNumberByRoundingOff:mainIn/unit precise:precise needsymbol:NO];
    self.mOutValueLabel.text =[UPMarketUICalculateUtil transNumberByRoundingOff:mainOut/unit precise:precise needsymbol:NO];
    self.mJInValueLabel.text = [UPMarketUICalculateUtil transNumberByRoundingOff:mainTotal/unit precise:precise needsymbol:NO];

    self.mInValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainIn baseValue:0];
    self.mOutValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainOut baseValue:0];
    self.mJInValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainTotal baseValue:0];
}

/// 直方图
- (void)setupHistogramView {

    double superValue = self.capitalFlowData.superIn - self.capitalFlowData.superOut;
    double bigValue = self.capitalFlowData.bigIn - self.capitalFlowData.bigOut;
    double midValue = self.capitalFlowData.midIn - self.capitalFlowData.midOut;
    double smallValue = self.capitalFlowData.smallIn - self.capitalFlowData.smallOut;

    NSArray *titles = @[@"超大单", @"大单", @"中单", @"小单"];

    NSArray<NSNumber *> *vols = @[@(superValue),@(bigValue),@(midValue),@(smallValue)];

    self.histogramView.dataArray = vols;
    self.histogramView.titleArray = titles;
    self.histogramView.lineWidth = UPWidth(20);

    [self.histogramView draw];

}

- (void)setErrorViewHidden:(BOOL)isHidden {
    if (isHidden) {
        self.errorView.hidden = YES;
        self.intoBackView.hidden = NO;
    } else {
        self.errorView.hidden = NO;
        self.intoBackView.hidden = YES;
    }
}

// MARK: Getter & Setter
- (UILabel *)titleLable {
    if (!_titleLable) {
        _titleLable = [UILabel new];
        _titleLable.text = @"今日资金流向(万元)";
        _titleLable.textColor = UIColor.up_textPrimaryColor;
        _titleLable.font = [UIFont up_boldFontOfSize:UPWidth(17)];
    }
    return _titleLable;
}

- (UIButton *)flowintoBtn {
    if (!_flowintoBtn) {
        _flowintoBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_flowintoBtn setImage:UPTImg(@"指标设置/指标设置-问号") forState:UIControlStateNormal];
        [_flowintoBtn addTarget:self action:@selector(showFlowIntoDetail) forControlEvents:UIControlEventTouchUpInside];
    }
    return _flowintoBtn;
}

- (UIView *)intoBackView {
    if (!_intoBackView) {
        _intoBackView = [UIView new];
        _intoBackView.backgroundColor = UIColor.clearColor;
    }
    return _intoBackView;
}

- (UPErrorView *)errorView {
    if(!_errorView) {
        _errorView = [[UPErrorView alloc] init];
        _errorView.title = @"暂无数据";
        _errorView.hidden = YES;
    }

    return _errorView;
}

//- (UIView *)intoMark {
//    if (!_intoMark) {
//        _intoMark = [UIView new];
//        _intoMark.backgroundColor = UIColor.upmarketui_riseColor;
//        _intoMark.layer.masksToBounds = YES;
//        _intoMark.layer.cornerRadius = UPWidth(3);
//    }
//    return _intoMark;
//}
//- (UILabel *)intoLabel {
//    if (!_intoLabel) {
//        _intoLabel = [UILabel new];
//        _intoLabel.text = @"流入";
//        _intoLabel.textColor = UIColor.up_textSecondary1Color;
//        _intoLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
//    }
//    return _intoLabel;
//}
//- (UIView *)outMark {
//    if (!_outMark) {
//        _outMark = [UIView new];
//        _outMark.backgroundColor = UIColor.upmarketui_fallColor;
//        _outMark.layer.masksToBounds = YES;
//        _outMark.layer.cornerRadius = UPWidth(3);
//    }
//    return _outMark;
//}
//- (UILabel *)outLabel {
//    if (!_outLabel) {
//        _outLabel = [UILabel new];
//        _outLabel.text = @"流出";
//        _outLabel.textColor = UIColor.up_textSecondary1Color;
//        _outLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
//    }
//    return _outLabel;
//}
//- (UILabel *)unitLabel {
//    if (!_unitLabel) {
//        _unitLabel = [UILabel new];
//        _unitLabel.text = @"单位：万元";
//        _unitLabel.textColor = UIColor.up_textSecondary1Color;
//        _unitLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
//        _unitLabel.textAlignment = NSTextAlignmentRight;
//    }
//    return _unitLabel;
//}

- (UILabel *)mInTitleLabel {
    if (!_mInTitleLabel) {
        _mInTitleLabel = [UILabel new];
        _mInTitleLabel.text = @"主力流入";
        _mInTitleLabel.textColor = UIColor.up_textPrimaryColor;
        _mInTitleLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        [_mInTitleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _mInTitleLabel;
}

- (UILabel *)mInValueLabel {
    if (!_mInValueLabel) {
        _mInValueLabel = [UILabel new];
        _mInValueLabel.text = @"--";
        _mInValueLabel.textColor = UIColor.upmarketui_equalColor;
        _mInValueLabel.font = [UIFont up_mediumFontOfSize:UPWidth(14)];
        _mInValueLabel.textAlignment = NSTextAlignmentRight;
        _mInValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _mInValueLabel;
}

- (UILabel *)mOutTitleLabel {
    if (!_mOutTitleLabel) {
        _mOutTitleLabel = [UILabel new];
        _mOutTitleLabel.text = @"主力流出";
        _mOutTitleLabel.textColor = UIColor.up_textPrimaryColor;
        _mOutTitleLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        [_mOutTitleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _mOutTitleLabel;
}

- (UILabel *)mOutValueLabel {
    if (!_mOutValueLabel) {
        _mOutValueLabel = [UILabel new];
        _mOutValueLabel.text = @"--";
        _mOutValueLabel.textColor = UIColor.upmarketui_equalColor;
        _mOutValueLabel.font = [UIFont up_mediumFontOfSize:UPWidth(14)];
        _mOutValueLabel.textAlignment = NSTextAlignmentRight;
        _mOutValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _mOutValueLabel;
}

- (UILabel *)mJInTitleLabel {
    if (!_mJInTitleLabel) {
        _mJInTitleLabel = [UILabel new];
        _mJInTitleLabel.text = @"主力净流入";
        _mJInTitleLabel.textColor = UIColor.up_textPrimaryColor;
        _mJInTitleLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        [_mJInTitleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _mJInTitleLabel;
}

- (UILabel *)mJInValueLabel {
    if (!_mJInValueLabel) {
        _mJInValueLabel = [UILabel new];
        _mJInValueLabel.text = @"--";
        _mJInValueLabel.textColor = UIColor.upmarketui_equalColor;
        _mJInValueLabel.font = [UIFont up_mediumFontOfSize:UPWidth(14)];
        _mJInValueLabel.textAlignment = NSTextAlignmentRight;
        _mJInValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _mJInValueLabel;
}

- (UPMarket2SDIStockHistogramView *)histogramView {
    if (!_histogramView) {
        _histogramView = [UPMarket2SDIStockHistogramView new];
    }
    return _histogramView;
}

- (void)setStockHq:(UPHqStockHq *)stockHq {
    [super setStockHq:stockHq];
    [self requestData];
}

- (void)setCapitalFlowData:(UPHqCapitalFlowData *)capitalFlowData {
    _capitalFlowData = capitalFlowData;

    [self updateNoDataView];
}
// MARK: requestData
- (void)requestData {
    if (!self.stockHq) return;

    // 指数使用UP数据，个股使用服务器数据
    if ([self.stockHq isIndexOfMarketHS] || self.stockHq.stockCategory == UPMarketStockCategory_INDEX) {
        // 指数使用UP数据
        UPMarketCapitalFlowDataReq *req = [[UPMarketCapitalFlowDataReq alloc] initWithSetCode:self.stockHq.setCode code:self.stockHq.code];
        req.type = UPMarketMoneyTypeDay_1;

        WeakSelf(weakSelf);
        [UPMarketManager requestStockCapitalFlowData:req completionHandler:^(UPMarketCapitalFlowDataRsp *rsp, NSError *error) {
            if (!error) {
                __strong typeof(weakSelf) strongSelf = weakSelf;

                if (strongSelf) {
                    if (rsp.capitalFlowData) {
                        strongSelf.capitalFlowData = rsp.capitalFlowData;
                    } else {
                        [weakSelf setErrorViewHidden:NO];
                    }
                }
            } else {
                [weakSelf setErrorViewHidden:NO];
            }
        }];
    } else {
        // 个股使用服务器数据
        [FMUPDataTool requestStockTodayNetInflowWithStockCode:[FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:self.stockHq.setCode code:self.stockHq.code]] success:^(NSDictionary *dic) {
            NSDictionary *dict = dic[@"data"];
            if (dict) {
                [self updateWithServerData:dict];
            }
        } failure:^{
            [self setErrorViewHidden:NO];
        }];
    }
}

// 处理服务器数据（个股使用）
- (void)updateWithServerData:(NSDictionary *)dict {
    if (!dict) {
        [self setErrorViewHidden:NO];
        return;
    }

    [self setErrorViewHidden:YES];

    // 更新文字标签
    long long mainIn = [dict[@"mainNetInflow"] longLongValue];
    long long mainOut = -[dict[@"mainNetOutflow"] longLongValue];
    long long mainTotal = mainIn + mainOut;

    CGFloat unit = 10000.0;  // 万
    NSInteger precise= 0;
    self.mInValueLabel.text = [UPMarketUICalculateUtil transNumberByRoundingOff:mainIn/unit precise:precise needsymbol:NO];
    self.mOutValueLabel.text =[UPMarketUICalculateUtil transNumberByRoundingOff:mainOut/unit precise:precise needsymbol:NO];
    self.mJInValueLabel.text = [UPMarketUICalculateUtil transNumberByRoundingOff:mainTotal/unit precise:precise needsymbol:NO];

    self.mInValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainIn baseValue:0];
    self.mOutValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainOut baseValue:0];
    self.mJInValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:mainTotal baseValue:0];

    // 更新直方图
    double superValue = [dict[@"superIn"] doubleValue];
    double bigValue = [dict[@"largeIn"] doubleValue];
    double midValue = [dict[@"mediumIn"] doubleValue];
    double smallValue = [dict[@"littleIn"] doubleValue];

    NSArray *titles = @[@"超大单", @"大单", @"中单", @"小单"];
    NSArray<NSNumber *> *vols = @[@(superValue),@(bigValue),@(midValue),@(smallValue)];

    self.histogramView.dataArray = vols;
    self.histogramView.titleArray = titles;
    self.histogramView.lineWidth = UPWidth(15);

    [self.histogramView draw];
}

@end
