//
//  UPMarket2SDIDay20FlowDirectionView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SDIDay20FlowDirectionView.h"
#import "UPMarket2SDIStockFundMinuteView.h"
#import "UPMarket2StockUIUtil.h"
#import "FMUPDataTool.h"

@interface UPMarket2SDIDay20FlowDirectionView ()

@property (nonatomic, strong) UILabel *titleLable;  // 标题

@property (nonatomic, strong) UPMarket2SDIStockFundMinuteView *stockFundMinuteView;    // 主力净流入图

@property (nonatomic, strong) UIView *bottomBGView;    // 资金流入背景

@property (nonatomic, strong) UILabel *day3TitleLabel;  // 3日净流入标题
@property (nonatomic, strong) UILabel *day3ValueLabel;  // 3日净流入值
@property (nonatomic, strong) UILabel *day5TitleLabel;  // 5日净流入标题
@property (nonatomic, strong) UILabel *day5ValueLabel;  // 5日净流入值
@property (nonatomic, strong) UILabel *day10TitleLabel;  // 10日净流入标题
@property (nonatomic, strong) UILabel *day10ValueLabel;  // 10日净流入值
@property (nonatomic, strong) UILabel *day20TitleLabel;  // 20日净流入标题
@property (nonatomic, strong) UILabel *day20ValueLabel;  // 20日净流入值

@property (nonatomic, strong) UPMarketMonitor *monitor;
@end

@implementation UPMarket2SDIDay20FlowDirectionView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViews];
        [self setConstraints];
        
        [self setBottomBGViewSubViews];
    }
    return self;
}

- (void)setupViews {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.titleLable];
    [self addSubview:self.stockFundMinuteView];
    [self addSubview:self.bottomBGView];
}

- (void)setConstraints {
    CGFloat padding_h = UPWidth(15);
    
    [self.titleLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(UPWidth(25));
        make.top.right.equalTo(self);
        make.height.equalTo(@UPWidth(46));
    }];
    
    UIView *redView = [[UIView alloc] init];
    [self addSubview:redView];
    redView.backgroundColor = HexColor(@"#FC0002");
    [redView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(padding_h);
        make.centerY.equalTo(self.titleLable);
        make.width.equalTo(@UPWidth(3));
        make.height.equalTo(@UPWidth(12));
    }];
    redView.layer.cornerRadius = UPWidth(1.5);
    redView.layer.masksToBounds = YES;
    
    [self.stockFundMinuteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.titleLable.mas_bottom);
        make.bottom.equalTo(self.bottomBGView.mas_top);
    }];
    
    [self.bottomBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(padding_h);
        make.right.equalTo(self).offset(-padding_h);
        make.bottom.equalTo(self).offset(-padding_h);
        make.height.equalTo(@UPWidth(70));
    }];
}

- (void)setBottomBGViewSubViews {
    [self.bottomBGView addSubview:self.day3TitleLabel];
    [self.bottomBGView addSubview:self.day3ValueLabel];
    [self.bottomBGView addSubview:self.day5TitleLabel];
    [self.bottomBGView addSubview:self.day5ValueLabel];
    [self.bottomBGView addSubview:self.day10TitleLabel];
    [self.bottomBGView addSubview:self.day10ValueLabel];
    [self.bottomBGView addSubview:self.day20TitleLabel];
    [self.bottomBGView addSubview:self.day20ValueLabel];
    
    [self.day3TitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bottomBGView.mas_left);
        make.top.equalTo(self.bottomBGView.mas_top).offset(UPWidth(15));
        make.width.equalTo(self.bottomBGView.mas_width).multipliedBy(0.25);
        make.height.equalTo(@UPWidth(12));
    }];
    [self.day3ValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day3TitleLabel.mas_left);
        make.top.equalTo(self.day3TitleLabel.mas_bottom).offset(UPWidth(8));
        make.width.equalTo(self.day3TitleLabel.mas_width);
        make.height.equalTo(@UPWidth(15));
    }];
    
    [self.day5TitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day3TitleLabel.mas_right);
        make.top.equalTo(self.day3TitleLabel.mas_top);
        make.width.height.equalTo(self.day3TitleLabel);
    }];
    [self.day5ValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day3ValueLabel.mas_right);
        make.top.equalTo(self.day3ValueLabel.mas_top);
        make.width.height.equalTo(self.day3ValueLabel);
    }];
    
    [self.day10TitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day5TitleLabel.mas_right);
        make.top.equalTo(self.day3TitleLabel.mas_top);
        make.width.height.equalTo(self.day3TitleLabel);
    }];
    [self.day10ValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day5ValueLabel.mas_right);
        make.top.equalTo(self.day3ValueLabel.mas_top);
        make.width.height.equalTo(self.day3ValueLabel);
    }];
    
    [self.day20TitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day10TitleLabel.mas_right);
        make.top.equalTo(self.day3TitleLabel.mas_top);
        make.width.height.equalTo(self.day3TitleLabel);
    }];
    [self.day20ValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.day10ValueLabel.mas_right);
        make.top.equalTo(self.day3ValueLabel.mas_top);
        make.width.height.equalTo(self.day3ValueLabel);
    }];
}

// MARK: Private

- (void)viewDidAppear {
    [self requestData];
}

- (void)viewDidDisappear {
    [self.monitor stopMonitorWithTag:0];
    [self.monitor stopMonitor];
}

- (void)updateTextLabelWithDic:(NSDictionary *)dic {
    self.day3ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@([dic[@"sumLast3days"] floatValue]) afterPoint:2];
    self.day3ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:[dic[@"sumLast3days"] floatValue] baseValue:0];

    self.day5ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@([dic[@"sumLast5days"] floatValue]) afterPoint:2];
    self.day5ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:[dic[@"sumLast5days"] floatValue] baseValue:0];

    self.day10ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@([dic[@"sumLast10days"] floatValue]) afterPoint:2];
    self.day10ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:[dic[@"sumLast10days"] floatValue] baseValue:0];

    self.day20ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@([dic[@"sumLast20days"] floatValue]) afterPoint:2];
    self.day20ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:[dic[@"sumLast20days"] floatValue] baseValue:0];
}

- (void)updateTextLabelWithDDEArray:(NSArray<UPMarketDDEInfo *> *)ddeArray {
    if (!ddeArray || ddeArray.count == 0) {
        return;
    }

    // 计算3日、5日、10日、20日的主力净流入
    double sum3days = 0, sum5days = 0, sum10days = 0, sum20days = 0;

    for (int i = 0; i < ddeArray.count && i < 20; i++) {
        UPMarketDDEInfo *ddeInfo = ddeArray[i];
        double mainNetInflow = 0;

        if (ddeInfo.amountItem) {
            // 主力净流入 = (超大单流入 + 大单流入) - (超大单流出 + 大单流出)
            mainNetInflow = (ddeInfo.amountItem.superIn + ddeInfo.amountItem.bigIn) -
                           (ddeInfo.amountItem.superOut + ddeInfo.amountItem.bigOut);
        }

        if (i < 3) sum3days += mainNetInflow;
        if (i < 5) sum5days += mainNetInflow;
        if (i < 10) sum10days += mainNetInflow;
        if (i < 20) sum20days += mainNetInflow;
    }

    self.day3ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@(sum3days) afterPoint:2];
    self.day3ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:sum3days baseValue:0];

    self.day5ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@(sum5days) afterPoint:2];
    self.day5ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:sum5days baseValue:0];

    self.day10ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@(sum10days) afterPoint:2];
    self.day10ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:sum10days baseValue:0];

    self.day20ValueLabel.text = [UPMarketUICalculateUtil transStringWithUnitOfValue:@(sum20days) afterPoint:2];
    self.day20ValueLabel.textColor = [UPMarket2StockUIUtil getRiseFallTextColor:sum20days baseValue:0];
}
// MARK: Getter & Setter
- (UILabel *)titleLable {
    if (!_titleLable) {
        _titleLable = [UILabel new];
        _titleLable.text = @"近20日主力资金流向";
        _titleLable.textColor = UIColor.up_textPrimaryColor;
        _titleLable.font = [UIFont up_boldFontOfSize:UPWidth(17)];
    }
    return _titleLable;
}

- (UPMarket2SDIStockFundMinuteView *)stockFundMinuteView {
    if (!_stockFundMinuteView) {
        _stockFundMinuteView = [UPMarket2SDIStockFundMinuteView new];
    }
    return _stockFundMinuteView;
}

- (UIView *)bottomBGView {
    if (!_bottomBGView) {
        _bottomBGView = [UIView new];
        _bottomBGView.backgroundColor = UIColor.upmarket2_longPressMaskBgColor;
    }
    return _bottomBGView;
}

- (UILabel *)day3TitleLabel {
    if (!_day3TitleLabel) {
        _day3TitleLabel = [UILabel new];
        _day3TitleLabel.text = @"3日净流入";
        _day3TitleLabel.textColor = UIColor.up_textSecondaryColor;
        _day3TitleLabel.exactScaleFont = [UIFont up_fontOfSize:UPWidth(12)];
        _day3TitleLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _day3TitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _day3TitleLabel;
}
- (UILabel *)day3ValueLabel {
    if (!_day3ValueLabel) {
        _day3ValueLabel = [UILabel new];
        _day3ValueLabel.textColor = UIColor.upmarketui_riseColor;
        _day3ValueLabel.exactScaleFont = [UIFont up_boldFontOfSize:UPWidth(13)];
        _day3ValueLabel.font = [UIFont up_boldFontOfSize:UPWidth(15)];
        _day3ValueLabel.textAlignment = NSTextAlignmentCenter;
        _day3ValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _day3ValueLabel;
}

- (UILabel *)day5TitleLabel {
    if (!_day5TitleLabel) {
        _day5TitleLabel = [UILabel new];
        _day5TitleLabel.text = @"5日净流入";
        _day5TitleLabel.textColor = UIColor.up_textSecondaryColor;
        _day5TitleLabel.exactScaleFont = [UIFont up_fontOfSize:UPWidth(12)];
        _day5TitleLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _day5TitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _day5TitleLabel;
}
- (UILabel *)day5ValueLabel {
    if (!_day5ValueLabel) {
        _day5ValueLabel = [UILabel new];
        _day5ValueLabel.textColor = UIColor.upmarketui_riseColor;
        _day5ValueLabel.exactScaleFont = [UIFont up_boldFontOfSize:UPWidth(15)];
        _day5ValueLabel.font = [UIFont up_boldFontOfSize:UPWidth(13)];
        _day5ValueLabel.textAlignment = NSTextAlignmentCenter;
        _day5ValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _day5ValueLabel;
}

- (UILabel *)day10TitleLabel {
    if (!_day10TitleLabel) {
        _day10TitleLabel = [UILabel new];
        _day10TitleLabel.text = @"10日净流入";
        _day10TitleLabel.textColor = UIColor.up_textSecondaryColor;
        _day10TitleLabel.exactScaleFont = [UIFont up_fontOfSize:UPWidth(12)];
        _day10TitleLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _day10TitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _day10TitleLabel;
}

- (UILabel *)day10ValueLabel {
    if (!_day10ValueLabel) {
        _day10ValueLabel = [UILabel new];
        _day10ValueLabel.textColor = UIColor.upmarketui_riseColor;
        _day10ValueLabel.exactScaleFont = [UIFont up_boldFontOfSize:UPWidth(15)];
        _day10ValueLabel.font = [UIFont up_boldFontOfSize:UPWidth(13)];
        _day10ValueLabel.textAlignment = NSTextAlignmentCenter;
        _day10ValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _day10ValueLabel;
}  

- (UILabel *)day20TitleLabel {
    if (!_day20TitleLabel) {
        _day20TitleLabel = [UILabel new];
        _day20TitleLabel.text = @"20日净流入";
        _day20TitleLabel.textColor = UIColor.up_textSecondaryColor;
        _day20TitleLabel.exactScaleFont = [UIFont up_fontOfSize:UPWidth(12)];
        _day20TitleLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _day20TitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _day20TitleLabel;
}
- (UILabel *)day20ValueLabel {
    if (!_day20ValueLabel) {
        _day20ValueLabel = [UILabel new];
        _day20ValueLabel.textColor = UIColor.upmarketui_riseColor;
        _day20ValueLabel.exactScaleFont = [UIFont up_boldFontOfSize:UPWidth(15)];
        _day20ValueLabel.font = [UIFont up_boldFontOfSize:UPWidth(13)];
        _day20ValueLabel.textAlignment = NSTextAlignmentCenter;
        _day20ValueLabel.adjustsFontSizeToFitWidth = YES;
    }
    return _day20ValueLabel;
}

- (UPMarketMonitor *)monitor {
    if (!_monitor) {
        _monitor = [[UPMarketMonitor alloc] init];

    }
    return _monitor;
}

- (void)setStockHq:(UPHqStockHq *)stockHq {
    [super setStockHq:stockHq];
    [self requestData];
    
    self.stockFundMinuteView.stockHq = stockHq;
}

// MARK: requestData
- (void)requestData {
    if (!self.stockHq) return;

    // 指数使用UP数据，个股使用服务器数据
    if (self.stockHq.category == UPMarketStockCategory_INDEX) {
        // 指数使用UP数据
        UPMarketDDEReq *ddeReq = [[UPMarketDDEReq alloc] initWithSetCode:self.stockHq.setCode code:self.stockHq.code];

        ddeReq.startNo = 0;
        ddeReq.type = UPMarketDDETypeDay;
        ddeReq.wantNum = 20;

        WeakSelf(weakSelf);
        [self.monitor stopMonitorWithTag:0];

        [self.monitor startMonitorStockDDE:ddeReq tag:0 completionHandler:^(UPMarketDDERsp *rsp, NSError *error) {
            if (!error) {
                __strong typeof(weakSelf) strongSelf = weakSelf;

                if (strongSelf) {
                    strongSelf.stockFundMinuteView.ddeDataList = rsp.ddeArray;
                    [strongSelf updateTextLabelWithDDEArray:rsp.ddeArray];
                }
            }
         }];
    } else {
        // 个股使用服务器数据
        [FMUPDataTool requestStockTwentyNetInflowWithStockCode:[FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:self.stockHq.setCode code:self.stockHq.code]] success:^(NSDictionary *dic) {
            NSDictionary *dict = dic[@"data"];
            NSArray *arr = dict[@"netInflowDtoList"];
            if ([arr isKindOfClass:[NSArray class]] && arr.count) {
                NSMutableArray *mArr = [NSMutableArray array];
                for (NSDictionary *innerDict in arr) {
                    UPMarketDDEInfo *model = [UPMarketDDEInfo new];
                    UPHqCapitalFlowData *amountItem = [UPHqCapitalFlowData new];
                    amountItem.superIn = [innerDict[@"buyValue"] doubleValue];
                    model.time = [innerDict[@"tradingdate"] longLongValue] / 1000;
                    model.amountItem = amountItem;
                    [mArr addObject:model];
                    if (mArr.count >= 20) {
                        break;
                    }
                }
                self.stockFundMinuteView.ddeDataList = mArr;
            }

            [self updateTextLabelWithDic:dict];
        } failure:^{
        }];
    }


}
@end
